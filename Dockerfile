# Use Node.js 18 on Debian slim for DuckDB compatibility
FROM node:18-slim AS base
WORKDIR /app

# -----------------------------
# Development stage
# -----------------------------
FROM base AS development
# Only copy package.json so dependency layer caches well
COPY package.json ./
# Install all deps (including dev) without needing a lockfile
RUN npm install --no-audit --no-fund
# Now copy the rest of the app
COPY . .
EXPOSE 8080
CMD ["npm", "run", "dev"]

# -----------------------------
# Build stage
# -----------------------------
FROM base AS build
ENV NODE_ENV=development
# Only copy package.json so this step doesn't require package-lock or node_modules
COPY package.json ./
# Install dev deps needed to build TypeScript, etc.
RUN npm install --no-audit --no-fund
# Copy source to build
COPY . .
# Build to /app/dist
RUN npm run build
# Remove dev dependencies so we only keep production deps for the final image
RUN npm prune --omit=dev

# -----------------------------
# Production runtime stage
# -----------------------------
FROM node:18-slim AS production
WORKDIR /app
ENV NODE_ENV=production

# Create non-root user
RUN groupadd -r nodejs && useradd -r -g nodejs nodejs

# Copy the compiled app and the minimal runtime deps
COPY --from=build --chown=nodejs:nodejs /app/dist ./dist
COPY --from=build --chown=nodejs:nodejs /app/package.json ./package.json
COPY --from=build --chown=nodejs:nodejs /app/node_modules ./node_modules

USER nodejs
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:8080/', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start production server
CMD ["npm", "start"]
