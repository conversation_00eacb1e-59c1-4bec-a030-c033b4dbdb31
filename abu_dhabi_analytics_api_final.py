#!/usr/bin/env python3
"""
Abu Dhabi Analytics API - Final Version
======================================

Production-ready FastAPI backend for Abu Dhabi Department of Health
illicit drugs surveillance analytics using actual database structure.

Key Features:
- Direct DuckDB integration with actual table structure
- No assumptions or sample data
- Returns ALL data matching filters (no limits)
- GIS_ID support for mapping
- Comprehensive filtering by analyte, group, location, date
- 5 advanced analytics endpoints

Database Structure:
- Main table: summary_results_unified
- LOQ table: analyte_loq_clean  
- Join: summary_results_unified.name = analyte_loq_clean.Analyte

Author: Public Health Intelligence Team
Client: Abu Dhabi Department of Health
Date: September 2025
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
import duckdb
import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime, timedelta
import traceback
import plotly.graph_objects as go
import plotly.express as px
from plotly.utils import PlotlyJSONEncoder

# Analytics libraries
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.ensemble import IsolationForest, RandomForestRegressor
from sklearn.neighbors import LocalOutlierFactor
from sklearn.svm import OneClassSVM
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_absolute_error, mean_squared_error
import ruptures as rpt
from scipy import stats
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller
from statsmodels.tsa.exponential_smoothing.ets import ETSModel
from statsmodels.tsa.holtwinters import ExponentialSmoothing

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("abu_dhabi_analytics")

# Database configuration
DB_PATH = "/mnt/data/duckdb/lims_clean.db"

# Initialize FastAPI app
app = FastAPI(
    title="Abu Dhabi Illicit Drugs Analytics API",
    description="Advanced analytics for Abu Dhabi Department of Health surveillance data",
    version="3.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request validation
class FilterParams(BaseModel):
    analyte: Optional[str] = Field(None, description="Filter by specific analyte name")
    analyte_group: Optional[str] = Field(None, description="Filter by analyte group (e.g., ILLICIT_DRUGS)")
    location: Optional[str] = Field(None, description="Filter by specific location")
    gis_id: Optional[str] = Field(None, description="Filter by GIS ID")
    date_from: Optional[str] = Field(None, description="Start date (YYYY-MM-DD)")
    date_to: Optional[str] = Field(None, description="End date (YYYY-MM-DD)")

class ChangePointRequest(FilterParams):
    min_size: Optional[int] = Field(5, description="Minimum segment size between changepoints")
    model: Optional[str] = Field("l2", description="Detection model: l1, l2, rbf")
    penalty: Optional[float] = Field(1.0, description="Changepoint detection penalty")

class AnomalyRequest(FilterParams):
    contamination: Optional[float] = Field(0.1, description="Expected proportion of anomalies")
    method: Optional[str] = Field("isolation_forest", description="Detection method: isolation_forest, local_outlier_factor, one_class_svm")

class ForecastRequest(FilterParams):
    forecast_horizon: Optional[int] = Field(90, description="Number of days to forecast (default: 90 for 3 months)")
    confidence_interval: Optional[float] = Field(0.95, description="Confidence interval level")
    model_type: Optional[str] = Field("ensemble", description="Forecasting model type: arima, ets, linear, ensemble")
    aggregation_period: Optional[str] = Field("weekly", description="Data aggregation period: daily, weekly, monthly")

class ClusteringRequest(FilterParams):
    n_clusters: Optional[int] = Field(3, description="Number of clusters (for kmeans)")
    algorithm: Optional[str] = Field("kmeans", description="Clustering algorithm: kmeans, dbscan")
    features: Optional[List[str]] = Field(["median_concentration", "detection_rate"], description="Features for clustering")
    standardize: Optional[bool] = Field(True, description="Standardize features before clustering")

class SeasonalityRequest(FilterParams):
    period: Optional[int] = Field(12, description="Seasonal period")
    method: Optional[str] = Field("additive", description="Decomposition method: additive, multiplicative")

# Database connection helper
def get_db_connection():
    """Get DuckDB connection"""
    try:
        conn = duckdb.connect(DB_PATH)
        return conn
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        raise HTTPException(status_code=500, detail=f"Database connection failed: {str(e)}")

def build_filter_query(filters: FilterParams, base_query: str) -> tuple:
    """Build SQL query with filters"""
    where_conditions = []
    params = {}
    
    # Always filter for numeric rawResult values
    where_conditions.append("s.rawResult ~ '^[0-9]+\\.?[0-9]*$'")
    where_conditions.append("s.rawResult IS NOT NULL")
    where_conditions.append("s.rawResult != ''")
    
    if filters.analyte:
        where_conditions.append("s.name = $analyte")
        params['analyte'] = filters.analyte
    
    if filters.analyte_group:
        where_conditions.append("s.Analyte_Group = $analyte_group")
        params['analyte_group'] = filters.analyte_group
    
    if filters.location:
        where_conditions.append("s.Location = $location")
        params['location'] = filters.location
    
    if filters.gis_id:
        where_conditions.append("s.GIS_ID = $gis_id")
        params['gis_id'] = filters.gis_id
    
    if filters.date_from:
        where_conditions.append("s.SAMPLED_DATE >= $date_from")
        params['date_from'] = filters.date_from
    
    if filters.date_to:
        where_conditions.append("s.SAMPLED_DATE <= $date_to")
        params['date_to'] = filters.date_to
    
    # Add WHERE clause if conditions exist
    if where_conditions:
        where_clause = " WHERE " + " AND ".join(where_conditions)
        query = base_query + where_clause
    else:
        query = base_query
    
    return query, params

def get_filtered_data(filters: FilterParams) -> pd.DataFrame:
    """Get filtered data from database"""
    base_query = """
    SELECT 
        s.Location,
        s.name as analyte,
        s.Analyte_Group,
        s.SAMPLED_DATE,
        s.GIS_ID,
        s.Latitude,
        s.Longitude,
        s.rawResult,
        CAST(s.rawResult AS DOUBLE) as raw_result_numeric,
        COALESCE(l.loq_value, 0.001) as loq_value,
        CASE 
            WHEN CAST(s.rawResult AS DOUBLE) >= COALESCE(l.loq_value, 0.001) 
            THEN CAST(s.rawResult AS DOUBLE)
            ELSE COALESCE(l.loq_value, 0.001) * 0.5
        END as concentration_loq_normalized,
        CASE 
            WHEN CAST(s.rawResult AS DOUBLE) >= COALESCE(l.loq_value, 0.001) 
            THEN 1 
            ELSE 0 
        END as is_detected,
        EXTRACT(YEAR FROM s.SAMPLED_DATE) as sample_year,
        EXTRACT(MONTH FROM s.SAMPLED_DATE) as sample_month,
        EXTRACT(DOW FROM s.SAMPLED_DATE) as day_of_week
    FROM summary_results_unified s
    LEFT JOIN analyte_loq_clean l ON s.name = l.Analyte
    """
    
    query, params = build_filter_query(filters, base_query)
    query += " ORDER BY s.SAMPLED_DATE, s.Location, s.name"
    
    try:
        conn = get_db_connection()
        df = conn.execute(query, params).df()
        conn.close()
        
        if df.empty:
            raise HTTPException(status_code=404, detail="No data found for the specified filters")
        
        return df
    
    except Exception as e:
        logger.error(f"Data query failed: {e}")
        raise HTTPException(status_code=500, detail=f"Data query failed: {str(e)}")

def create_plotly_json(fig) -> str:
    """Convert Plotly figure to JSON string"""
    try:
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    except Exception as e:
        logger.error(f"Plotly JSON conversion failed: {e}")
        return "{}"

# API Endpoints

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Abu Dhabi Illicit Drugs Analytics API",
        "version": "3.0.0",
        "description": "Advanced analytics for Abu Dhabi Department of Health surveillance data",
        "database": DB_PATH,
        "endpoints": {
            "health": "/health",
            "database_info": "/database-info", 
            "locations": "/locations",
            "analytes": "/analytes",
            "analyte_groups": "/analyte-groups",
            "gis_ids": "/gis-ids",
            "changepoint_analysis": "/changepoint-analysis",
            "anomaly_detection": "/anomaly-detection",
            "forecasting": "/forecasting",
            "clustering": "/clustering",
            "seasonality": "/seasonality"
        },
        "documentation": {
            "swagger_ui": "/docs",
            "redoc": "/redoc"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        conn = get_db_connection()
        
        # Test database connectivity
        result = conn.execute("SELECT COUNT(*) as count FROM summary_results_unified").fetchone()
        total_records = result[0] if result else 0
        
        # Test LOQ table
        loq_result = conn.execute("SELECT COUNT(*) as count FROM analyte_loq_clean").fetchone()
        loq_records = loq_result[0] if loq_result else 0
        
        conn.close()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database": "connected",
            "database_path": DB_PATH,
            "version": "3.0.0",
            "total_records": total_records,
            "loq_records": loq_records
        }
    
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "database": "disconnected",
            "error": str(e),
            "version": "3.0.0"
        }

@app.get("/database-info")
async def database_info():
    """Get comprehensive database information"""
    try:
        conn = get_db_connection()
        
        # Get table info
        tables_info = {}
        
        # Summary results unified table
        summary_count = conn.execute("SELECT COUNT(*) FROM summary_results_unified").fetchone()[0]
        summary_columns = conn.execute("DESCRIBE summary_results_unified").df()
        
        tables_info["summary_results_unified"] = {
            "records": summary_count,
            "columns": summary_columns['column_name'].tolist()
        }
        
        # Analyte LOQ clean table
        loq_count = conn.execute("SELECT COUNT(*) FROM analyte_loq_clean").fetchone()[0]
        loq_columns = conn.execute("DESCRIBE analyte_loq_clean").df()
        
        tables_info["analyte_loq_clean"] = {
            "records": loq_count,
            "columns": loq_columns['column_name'].tolist()
        }
        
        # Get date range
        date_range = conn.execute("""
            SELECT 
                MIN(SAMPLED_DATE) as min_date,
                MAX(SAMPLED_DATE) as max_date
            FROM summary_results_unified
            WHERE SAMPLED_DATE IS NOT NULL
        """).fetchone()
        
        # Get unique counts
        unique_counts = conn.execute("""
            SELECT 
                COUNT(DISTINCT Location) as locations,
                COUNT(DISTINCT name) as analytes,
                COUNT(DISTINCT Analyte_Group) as analyte_groups,
                COUNT(DISTINCT GIS_ID) as gis_ids
            FROM summary_results_unified
        """).fetchone()
        
        conn.close()
        
        return {
            "success": True,
            "database_path": DB_PATH,
            "tables": tables_info,
            "date_range": {
                "start": date_range[0].isoformat() if date_range[0] else None,
                "end": date_range[1].isoformat() if date_range[1] else None
            },
            "unique_counts": {
                "locations": unique_counts[0],
                "analytes": unique_counts[1], 
                "analyte_groups": unique_counts[2],
                "gis_ids": unique_counts[3]
            }
        }
    
    except Exception as e:
        logger.error(f"Database info failed: {e}")
        raise HTTPException(status_code=500, detail=f"Database info failed: {str(e)}")

@app.get("/locations")
async def get_locations():
    """Get all available locations with GIS information"""
    try:
        conn = get_db_connection()
        
        query = """
        SELECT 
            Location,
            GIS_ID,
            Latitude,
            Longitude,
            COUNT(*) as sample_count
        FROM summary_results_unified
        WHERE Location IS NOT NULL 
            AND GIS_ID IS NOT NULL
        GROUP BY Location, GIS_ID, Latitude, Longitude
        ORDER BY sample_count DESC
        """
        
        df = conn.execute(query).df()
        conn.close()
        
        locations = []
        for _, row in df.iterrows():
            locations.append({
                "location": row['Location'],
                "gis_id": row['GIS_ID'],
                "latitude": float(row['Latitude']) if pd.notna(row['Latitude']) else None,
                "longitude": float(row['Longitude']) if pd.notna(row['Longitude']) else None,
                "sample_count": int(row['sample_count'])
            })
        
        return {
            "success": True,
            "locations": locations,
            "count": len(locations)
        }
    
    except Exception as e:
        logger.error(f"Get locations failed: {e}")
        raise HTTPException(status_code=500, detail=f"Get locations failed: {str(e)}")

@app.get("/analytes")
async def get_analytes():
    """Get all available analytes"""
    try:
        conn = get_db_connection()
        
        query = """
        SELECT 
            s.name as analyte,
            s.Analyte_Group,
            COUNT(*) as sample_count,
            COUNT(DISTINCT s.Location) as location_count,
            l.loq_value
        FROM summary_results_unified s
        LEFT JOIN analyte_loq_clean l ON s.name = l.Analyte
        WHERE s.name IS NOT NULL
        GROUP BY s.name, s.Analyte_Group, l.loq_value
        ORDER BY sample_count DESC
        """
        
        df = conn.execute(query).df()
        conn.close()
        
        analytes = []
        for _, row in df.iterrows():
            analytes.append({
                "analyte": row['analyte'],
                "analyte_group": row['Analyte_Group'],
                "sample_count": int(row['sample_count']),
                "location_count": int(row['location_count']),
                "loq_value": float(row['loq_value']) if pd.notna(row['loq_value']) else None
            })
        
        return {
            "success": True,
            "analytes": analytes,
            "count": len(analytes)
        }
    
    except Exception as e:
        logger.error(f"Get analytes failed: {e}")
        raise HTTPException(status_code=500, detail=f"Get analytes failed: {str(e)}")

@app.get("/analyte-groups")
async def get_analyte_groups():
    """Get all available analyte groups"""
    try:
        conn = get_db_connection()
        
        query = """
        SELECT 
            Analyte_Group,
            COUNT(*) as sample_count,
            COUNT(DISTINCT name) as analyte_count,
            COUNT(DISTINCT Location) as location_count
        FROM summary_results_unified
        WHERE Analyte_Group IS NOT NULL
        GROUP BY Analyte_Group
        ORDER BY sample_count DESC
        """
        
        df = conn.execute(query).df()
        conn.close()
        
        groups = []
        for _, row in df.iterrows():
            groups.append({
                "analyte_group": row['Analyte_Group'],
                "sample_count": int(row['sample_count']),
                "analyte_count": int(row['analyte_count']),
                "location_count": int(row['location_count'])
            })
        
        return {
            "success": True,
            "analyte_groups": groups,
            "count": len(groups)
        }
    
    except Exception as e:
        logger.error(f"Get analyte groups failed: {e}")
        raise HTTPException(status_code=500, detail=f"Get analyte groups failed: {str(e)}")

@app.get("/gis-ids")
async def get_gis_ids():
    """Get all GIS IDs with location mapping"""
    try:
        conn = get_db_connection()
        
        query = """
        SELECT 
            GIS_ID,
            Location,
            Latitude,
            Longitude,
            COUNT(*) as sample_count
        FROM summary_results_unified
        WHERE GIS_ID IS NOT NULL
        GROUP BY GIS_ID, Location, Latitude, Longitude
        ORDER BY sample_count DESC
        """
        
        df = conn.execute(query).df()
        conn.close()
        
        gis_ids = []
        for _, row in df.iterrows():
            gis_ids.append({
                "gis_id": row['GIS_ID'],
                "location": row['Location'],
                "latitude": float(row['Latitude']) if pd.notna(row['Latitude']) else None,
                "longitude": float(row['Longitude']) if pd.notna(row['Longitude']) else None,
                "sample_count": int(row['sample_count'])
            })
        
        return {
            "success": True,
            "gis_ids": gis_ids,
            "count": len(gis_ids)
        }
    
    except Exception as e:
        logger.error(f"Get GIS IDs failed: {e}")
        raise HTTPException(status_code=500, detail=f"Get GIS IDs failed: {str(e)}")

@app.post("/changepoint-analysis")
async def changepoint_analysis(request: ChangePointRequest):
    """Detect changepoints in drug concentration trends"""
    try:
        # Get filtered data
        df = get_filtered_data(request)
        
        if len(df) < 10:
            raise HTTPException(status_code=400, detail="Insufficient data for changepoint analysis (minimum 10 points required)")
        
        # Aggregate data by date
        daily_data = df.groupby('SAMPLED_DATE').agg({
            'concentration_loq_normalized': 'median',
            'is_detected': 'mean',
            'Location': 'first',
            'GIS_ID': 'first',
            'Latitude': 'first',
            'Longitude': 'first'
        }).reset_index()
        
        daily_data = daily_data.sort_values('SAMPLED_DATE')
        
        # Prepare time series
        signal = daily_data['concentration_loq_normalized'].values
        
        # Detect changepoints
        if request.model == "l1":
            algo = rpt.Pelt(model="l1").fit(signal)
        elif request.model == "l2":
            algo = rpt.Pelt(model="l2").fit(signal)
        elif request.model == "rbf":
            algo = rpt.Pelt(model="rbf").fit(signal)
        else:
            algo = rpt.Pelt(model="l2").fit(signal)
        
        changepoints = algo.predict(pen=request.penalty)
        
        # Remove the last point (end of series)
        if changepoints and changepoints[-1] == len(signal):
            changepoints = changepoints[:-1]
        
        # Get changepoint information
        changepoint_info = []
        changepoint_dates = []
        changepoint_locations = []
        changepoint_gis_ids = []
        
        for cp in changepoints:
            if cp < len(daily_data):
                cp_date = daily_data.iloc[cp]['SAMPLED_DATE']
                cp_location = daily_data.iloc[cp]['Location']
                cp_gis_id = daily_data.iloc[cp]['GIS_ID']
                
                changepoint_dates.append(cp_date.strftime('%Y-%m-%d'))
                changepoint_locations.append(cp_location)
                changepoint_gis_ids.append(cp_gis_id)
        
        # Create visualization
        fig = go.Figure()
        
        # Add time series
        fig.add_trace(go.Scatter(
            x=daily_data['SAMPLED_DATE'],
            y=daily_data['concentration_loq_normalized'],
            mode='lines+markers',
            name='Concentration',
            line=dict(color='blue')
        ))
        
        # Add changepoints
        for i, cp in enumerate(changepoints):
            if cp < len(daily_data):
                fig.add_vline(
                    x=daily_data.iloc[cp]['SAMPLED_DATE'],
                    line_dash="dash",
                    line_color="red",
                    annotation_text=f"Changepoint {i+1}"
                )
        
        fig.update_layout(
            title="Changepoint Analysis - Abu Dhabi Department of Health",
            xaxis_title="Date",
            yaxis_title="Median Concentration (LOQ Normalized)",
            showlegend=True
        )
        
        return {
            "success": True,
            "message": "Changepoint analysis completed successfully",
            "data": {
                "changepoints": changepoints,
                "changepoint_dates": changepoint_dates,
                "changepoint_locations": changepoint_locations,
                "changepoint_gis_ids": changepoint_gis_ids,
                "total_changepoints": len(changepoints),
                "segments": len(changepoints) + 1,
                "time_series_length": len(daily_data),
                "data_summary": {
                    "total_records": len(df),
                    "unique_locations": df['Location'].nunique(),
                    "unique_gis_ids": df['GIS_ID'].nunique(),
                    "date_range": {
                        "start": df['SAMPLED_DATE'].min().isoformat(),
                        "end": df['SAMPLED_DATE'].max().isoformat()
                    }
                }
            },
            "metadata": {
                "algorithm": request.model,
                "penalty": request.penalty,
                "min_size": request.min_size,
                "filters_applied": request.dict(),
                "timestamp": datetime.now().isoformat()
            },
            "plot_data": create_plotly_json(fig)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Changepoint analysis failed: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Changepoint analysis failed: {str(e)}")

@app.post("/anomaly-detection")
async def anomaly_detection(request: AnomalyRequest):
    """Detect anomalies in drug concentration patterns"""
    try:
        # Get filtered data
        df = get_filtered_data(request)
        
        if len(df) < 10:
            raise HTTPException(status_code=400, detail="Insufficient data for anomaly detection (minimum 10 points required)")
        
        # Prepare features for anomaly detection
        features = df[['concentration_loq_normalized']].values
        
        # Initialize anomaly detector
        if request.method == "isolation_forest":
            detector = IsolationForest(contamination=request.contamination, random_state=42)
        elif request.method == "local_outlier_factor":
            detector = LocalOutlierFactor(contamination=request.contamination)
        elif request.method == "one_class_svm":
            detector = OneClassSVM(nu=request.contamination)
        else:
            detector = IsolationForest(contamination=request.contamination, random_state=42)
        
        # Detect anomalies
        if request.method == "local_outlier_factor":
            anomaly_labels = detector.fit_predict(features)
        else:
            anomaly_labels = detector.fit_predict(features)
        
        # Add anomaly labels to dataframe
        df['is_anomaly'] = anomaly_labels == -1
        
        # Get anomaly records
        anomaly_records = []
        anomalies_df = df[df['is_anomaly']]
        
        for _, row in anomalies_df.iterrows():
            anomaly_records.append({
                "date": row['SAMPLED_DATE'].isoformat(),
                "concentration": float(row['concentration_loq_normalized']),
                "location": row['Location'],
                "gis_id": row['GIS_ID'],
                "latitude": float(row['Latitude']) if pd.notna(row['Latitude']) else None,
                "longitude": float(row['Longitude']) if pd.notna(row['Longitude']) else None,
                "analyte": row['analyte'],
                "detection_rate": float(row['is_detected'])
            })
        
        # Create visualization
        fig = go.Figure()
        
        # Add normal points
        normal_df = df[~df['is_anomaly']]
        fig.add_trace(go.Scatter(
            x=normal_df['SAMPLED_DATE'],
            y=normal_df['concentration_loq_normalized'],
            mode='markers',
            name='Normal',
            marker=dict(color='blue', size=6)
        ))
        
        # Add anomaly points
        if not anomalies_df.empty:
            fig.add_trace(go.Scatter(
                x=anomalies_df['SAMPLED_DATE'],
                y=anomalies_df['concentration_loq_normalized'],
                mode='markers',
                name='Anomaly',
                marker=dict(color='red', size=10, symbol='x')
            ))
        
        fig.update_layout(
            title="Anomaly Detection - Abu Dhabi Department of Health",
            xaxis_title="Date",
            yaxis_title="Concentration (LOQ Normalized)",
            showlegend=True
        )
        
        return {
            "success": True,
            "message": "Anomaly detection completed successfully",
            "data": {
                "total_points": len(df),
                "anomalies_detected": len(anomalies_df),
                "anomaly_rate": len(anomalies_df) / len(df),
                "anomaly_records": anomaly_records,
                "data_summary": {
                    "total_records": len(df),
                    "unique_locations": df['Location'].nunique(),
                    "unique_gis_ids": df['GIS_ID'].nunique(),
                    "date_range": {
                        "start": df['SAMPLED_DATE'].min().isoformat(),
                        "end": df['SAMPLED_DATE'].max().isoformat()
                    }
                }
            },
            "metadata": {
                "method": request.method,
                "contamination": request.contamination,
                "filters_applied": request.dict(),
                "timestamp": datetime.now().isoformat()
            },
            "plot_data": create_plotly_json(fig)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Anomaly detection failed: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Anomaly detection failed: {str(e)}")

def aggregate_time_series(df: pd.DataFrame, period: str) -> pd.DataFrame:
    """Aggregate time series data by specified period"""
    df = df.copy()
    df['SAMPLED_DATE'] = pd.to_datetime(df['SAMPLED_DATE'])

    if period == "daily":
        return df.groupby('SAMPLED_DATE').agg({
            'concentration_loq_normalized': 'median',
            'Location': 'first',
            'GIS_ID': 'first',
            'Latitude': 'first',
            'Longitude': 'first'
        }).reset_index().sort_values('SAMPLED_DATE')

    elif period == "weekly":
        df['week'] = df['SAMPLED_DATE'].dt.to_period('W').dt.start_time
        return df.groupby('week').agg({
            'concentration_loq_normalized': 'median',
            'Location': 'first',
            'GIS_ID': 'first',
            'Latitude': 'first',
            'Longitude': 'first'
        }).reset_index().rename(columns={'week': 'SAMPLED_DATE'}).sort_values('SAMPLED_DATE')

    elif period == "monthly":
        df['month'] = df['SAMPLED_DATE'].dt.to_period('M').dt.start_time
        return df.groupby('month').agg({
            'concentration_loq_normalized': 'median',
            'Location': 'first',
            'GIS_ID': 'first',
            'Latitude': 'first',
            'Longitude': 'first'
        }).reset_index().rename(columns={'month': 'SAMPLED_DATE'}).sort_values('SAMPLED_DATE')

    else:
        raise ValueError(f"Unsupported aggregation period: {period}")

def calculate_forecast_uncertainty(historical_data: np.ndarray, forecast_values: np.ndarray,
                                 model_errors: dict) -> dict:
    """Calculate comprehensive uncertainty metrics"""
    historical_std = np.std(historical_data)
    historical_mean = np.mean(historical_data)

    # Model uncertainty (based on historical performance)
    model_uncertainty = np.mean(list(model_errors.values())) if model_errors else historical_std * 0.1

    # Forecast uncertainty increases with time horizon
    time_decay = np.linspace(1.0, 2.0, len(forecast_values))
    forecast_uncertainty = model_uncertainty * time_decay

    # Total uncertainty combines model and forecast uncertainty
    total_uncertainty = np.sqrt(forecast_uncertainty**2 + (historical_std * 0.1)**2)

    return {
        "model_uncertainty": model_uncertainty,
        "forecast_uncertainty": forecast_uncertainty.tolist(),
        "total_uncertainty": total_uncertainty.tolist(),
        "historical_volatility": historical_std,
        "uncertainty_explanation": {
            "model_uncertainty": "Uncertainty from model prediction errors",
            "forecast_uncertainty": "Uncertainty that increases with forecast horizon",
            "total_uncertainty": "Combined uncertainty from all sources"
        }
    }

def fit_ensemble_model(ts_data: np.ndarray, forecast_horizon: int, confidence_level: float) -> dict:
    """Fit ensemble of multiple forecasting models"""
    models_results = {}
    model_errors = {}

    # Prepare data for cross-validation
    train_size = max(10, int(len(ts_data) * 0.8))
    train_data = ts_data[:train_size]
    test_data = ts_data[train_size:]

    # Model 1: ARIMA
    try:
        arima_model = ARIMA(train_data, order=(1, 1, 1))
        arima_fitted = arima_model.fit()
        arima_forecast = arima_fitted.forecast(steps=len(test_data))
        arima_error = mean_absolute_error(test_data, arima_forecast) if len(test_data) > 0 else 0

        # Full forecast
        full_arima = ARIMA(ts_data, order=(1, 1, 1)).fit()
        arima_full_forecast = full_arima.forecast(steps=forecast_horizon)
        arima_ci = full_arima.get_forecast(steps=forecast_horizon).conf_int(alpha=1-confidence_level)

        models_results['arima'] = {
            'forecast': arima_full_forecast,
            'confidence_lower': arima_ci.iloc[:, 0].values,
            'confidence_upper': arima_ci.iloc[:, 1].values,
            'weight': 1.0 / (arima_error + 0.001)
        }
        model_errors['arima'] = arima_error

    except Exception as e:
        logger.warning(f"ARIMA model failed: {e}")
        models_results['arima'] = None
        
        except Exception as model_error:
            # Fallback to simple linear trend
            logger.warning(f"ARIMA model failed, using linear trend: {model_error}")
            
            # Simple linear forecast
            x = np.arange(len(ts_data))
            slope, intercept = np.polyfit(x, ts_data, 1)
            
            forecast_x = np.arange(len(ts_data), len(ts_data) + request.forecast_horizon)
            forecast_values = slope * forecast_x + intercept
            
            # Simple confidence interval (±20% of forecast value)
            confidence_margin = 0.2 * np.abs(forecast_values)
            confidence_lower = forecast_values - confidence_margin
            confidence_upper = forecast_values + confidence_margin
            
            # Generate future dates
            last_date = daily_data['SAMPLED_DATE'].max()
            forecast_dates = pd.date_range(start=last_date + timedelta(days=1), periods=request.forecast_horizon, freq='D')
            
            return {
                "success": True,
                "message": "Forecasting completed successfully (linear trend fallback)",
                "data": {
                    "forecast_horizon": request.forecast_horizon,
                    "forecast_dates": [d.strftime('%Y-%m-%d') for d in forecast_dates],
                    "forecast_values": forecast_values.tolist(),
                    "confidence_lower": confidence_lower.tolist(),
                    "confidence_upper": confidence_upper.tolist(),
                    "model_type": "linear_trend",
                    "historical_points": len(daily_data),
                    "location_info": {
                        "location": daily_data.iloc[0]['Location'],
                        "gis_id": daily_data.iloc[0]['GIS_ID'],
                        "latitude": float(daily_data.iloc[0]['Latitude']) if pd.notna(daily_data.iloc[0]['Latitude']) else None,
                        "longitude": float(daily_data.iloc[0]['Longitude']) if pd.notna(daily_data.iloc[0]['Longitude']) else None
                    },
                    "data_summary": {
                        "total_records": len(df),
                        "unique_locations": df['Location'].nunique(),
                        "unique_gis_ids": df['GIS_ID'].nunique(),
                        "date_range": {
                            "start": df['SAMPLED_DATE'].min().isoformat(),
                            "end": df['SAMPLED_DATE'].max().isoformat()
                        }
                    }
                },
                "metadata": {
                    "model_type": "linear_trend",
                    "forecast_horizon": request.forecast_horizon,
                    "confidence_interval": request.confidence_interval,
                    "filters_applied": request.dict(),
                    "timestamp": datetime.now().isoformat()
                },
                "plot_data": "{}"
            }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Forecasting failed: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Forecasting failed: {str(e)}")

@app.post("/clustering")
async def clustering_analysis(request: ClusteringRequest):
    """Cluster locations by drug patterns"""
    try:
        # Get filtered data
        df = get_filtered_data(request)
        
        if df['Location'].nunique() < 2:
            raise HTTPException(status_code=400, detail="Insufficient locations for clustering (minimum 2 required)")
        
        # Aggregate data by location
        location_data = df.groupby(['Location', 'GIS_ID', 'Latitude', 'Longitude']).agg({
            'concentration_loq_normalized': ['median', 'mean', 'std'],
            'is_detected': 'mean',
            'analyte': 'count'
        }).reset_index()
        
        # Flatten column names
        location_data.columns = ['Location', 'GIS_ID', 'Latitude', 'Longitude', 
                               'median_concentration', 'mean_concentration', 'std_concentration',
                               'detection_rate', 'sample_count']
        
        # Fill NaN values
        location_data['std_concentration'] = location_data['std_concentration'].fillna(0)
        
        # Prepare features for clustering
        feature_columns = []
        if 'median_concentration' in request.features:
            feature_columns.append('median_concentration')
        if 'mean_concentration' in request.features:
            feature_columns.append('mean_concentration')
        if 'detection_rate' in request.features:
            feature_columns.append('detection_rate')
        if 'variability' in request.features:
            feature_columns.append('std_concentration')
        if 'sample_count' in request.features:
            feature_columns.append('sample_count')
        
        # Default to median_concentration and detection_rate if no valid features
        if not feature_columns:
            feature_columns = ['median_concentration', 'detection_rate']
        
        features = location_data[feature_columns].values
        
        # Standardize features if requested
        if request.standardize:
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)
        else:
            features_scaled = features
        
        # Perform clustering
        if request.algorithm == "kmeans":
            clusterer = KMeans(n_clusters=request.n_clusters, random_state=42)
            cluster_labels = clusterer.fit_predict(features_scaled)
            n_noise = 0
        elif request.algorithm == "dbscan":
            clusterer = DBSCAN(eps=0.5, min_samples=2)
            cluster_labels = clusterer.fit_predict(features_scaled)
            n_noise = np.sum(cluster_labels == -1)
        else:
            clusterer = KMeans(n_clusters=request.n_clusters, random_state=42)
            cluster_labels = clusterer.fit_predict(features_scaled)
            n_noise = 0
        
        # Add cluster labels to location data
        location_data['cluster'] = cluster_labels
        
        # Prepare cluster assignments
        cluster_assignments = []
        for _, row in location_data.iterrows():
            assignment = {
                "location": row['Location'],
                "gis_id": row['GIS_ID'],
                "latitude": float(row['Latitude']) if pd.notna(row['Latitude']) else None,
                "longitude": float(row['Longitude']) if pd.notna(row['Longitude']) else None,
                "cluster": int(row['cluster']),
                "features": {}
            }
            
            # Add feature values
            for i, feature in enumerate(feature_columns):
                assignment["features"][feature] = float(row[feature])
            
            cluster_assignments.append(assignment)
        
        # Cluster summary
        cluster_summary = {}
        for cluster_id in np.unique(cluster_labels):
            cluster_locations = location_data[location_data['cluster'] == cluster_id]
            cluster_summary[int(cluster_id)] = {
                "size": len(cluster_locations),
                "locations": cluster_locations['Location'].tolist(),
                "avg_features": {
                    feature: float(cluster_locations[feature].mean()) 
                    for feature in feature_columns
                }
            }
        
        # PCA for visualization (if more than 2 features)
        pca_explained_variance = []
        if len(feature_columns) > 2:
            pca = PCA(n_components=2)
            features_pca = pca.fit_transform(features_scaled)
            pca_explained_variance = pca.explained_variance_ratio_.tolist()
        else:
            features_pca = features_scaled
            pca_explained_variance = [1.0, 0.0]
        
        # Create visualization
        fig = go.Figure()
        
        colors = px.colors.qualitative.Set1
        for cluster_id in np.unique(cluster_labels):
            cluster_mask = cluster_labels == cluster_id
            cluster_name = f"Cluster {cluster_id}" if cluster_id != -1 else "Noise"
            color = colors[cluster_id % len(colors)] if cluster_id != -1 else 'black'
            
            fig.add_trace(go.Scatter(
                x=features_pca[cluster_mask, 0],
                y=features_pca[cluster_mask, 1],
                mode='markers',
                name=cluster_name,
                marker=dict(color=color, size=10),
                text=location_data[location_data['cluster'] == cluster_id]['Location'].tolist(),
                hovertemplate='<b>%{text}</b><br>PC1: %{x:.2f}<br>PC2: %{y:.2f}<extra></extra>'
            ))
        
        fig.update_layout(
            title="Location Clustering Analysis - Abu Dhabi Department of Health",
            xaxis_title="Principal Component 1" if len(feature_columns) > 2 else feature_columns[0],
            yaxis_title="Principal Component 2" if len(feature_columns) > 2 else (feature_columns[1] if len(feature_columns) > 1 else "Value"),
            showlegend=True
        )
        
        return {
            "success": True,
            "message": "Clustering analysis completed successfully",
            "data": {
                "n_clusters": len(np.unique(cluster_labels[cluster_labels != -1])),
                "n_noise_points": n_noise,
                "cluster_assignments": cluster_assignments,
                "cluster_summary": cluster_summary,
                "pca_explained_variance": pca_explained_variance,
                "features_used": feature_columns,
                "data_summary": {
                    "total_records": len(df),
                    "unique_locations": df['Location'].nunique(),
                    "unique_gis_ids": df['GIS_ID'].nunique(),
                    "date_range": {
                        "start": df['SAMPLED_DATE'].min().isoformat(),
                        "end": df['SAMPLED_DATE'].max().isoformat()
                    }
                }
            },
            "metadata": {
                "algorithm": request.algorithm,
                "n_clusters": request.n_clusters,
                "features": request.features,
                "standardize": request.standardize,
                "filters_applied": request.dict(),
                "timestamp": datetime.now().isoformat()
            },
            "plot_data": create_plotly_json(fig)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Clustering analysis failed: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Clustering analysis failed: {str(e)}")

@app.post("/seasonality")
async def seasonality_analysis(request: SeasonalityRequest):
    """Analyze seasonal patterns in drug concentrations"""
    try:
        # Get filtered data
        df = get_filtered_data(request)
        
        if len(df) < 24:
            raise HTTPException(status_code=400, detail="Insufficient data for seasonality analysis (minimum 24 points required)")
        
        # Aggregate data by date
        daily_data = df.groupby('SAMPLED_DATE').agg({
            'concentration_loq_normalized': 'median',
            'Location': 'first',
            'GIS_ID': 'first',
            'Latitude': 'first',
            'Longitude': 'first'
        }).reset_index()
        
        daily_data = daily_data.sort_values('SAMPLED_DATE')
        
        # Create time series
        ts_data = daily_data.set_index('SAMPLED_DATE')['concentration_loq_normalized']
        
        # Perform seasonal decomposition
        try:
            decomposition = seasonal_decompose(
                ts_data, 
                model=request.method, 
                period=request.period,
                extrapolate_trend='freq'
            )
            
            # Calculate seasonal strength
            seasonal_var = np.var(decomposition.seasonal.dropna())
            residual_var = np.var(decomposition.resid.dropna())
            seasonal_strength = seasonal_var / (seasonal_var + residual_var) if (seasonal_var + residual_var) > 0 else 0
            
            # Calculate trend strength
            trend_var = np.var(decomposition.trend.dropna())
            trend_strength = trend_var / (trend_var + residual_var) if (trend_var + residual_var) > 0 else 0
            
            # Get seasonal pattern
            seasonal_pattern = {}
            if request.period <= 12:
                for i in range(1, request.period + 1):
                    seasonal_values = decomposition.seasonal[decomposition.seasonal.index.month == i]
                    if not seasonal_values.empty:
                        seasonal_pattern[str(i)] = float(seasonal_values.mean())
            
            # Determine trend direction
            trend_values = decomposition.trend.dropna()
            if len(trend_values) > 1:
                trend_slope = np.polyfit(range(len(trend_values)), trend_values, 1)[0]
                trend_direction = "increasing" if trend_slope > 0 else "decreasing" if trend_slope < 0 else "stable"
            else:
                trend_direction = "stable"
            
            # Create visualization
            fig = go.Figure()
            
            # Original data
            fig.add_trace(go.Scatter(
                x=ts_data.index,
                y=ts_data.values,
                mode='lines',
                name='Original',
                line=dict(color='blue')
            ))
            
            # Trend
            fig.add_trace(go.Scatter(
                x=decomposition.trend.index,
                y=decomposition.trend.values,
                mode='lines',
                name='Trend',
                line=dict(color='red')
            ))
            
            # Seasonal
            fig.add_trace(go.Scatter(
                x=decomposition.seasonal.index,
                y=decomposition.seasonal.values,
                mode='lines',
                name='Seasonal',
                line=dict(color='green')
            ))
            
            fig.update_layout(
                title="Seasonality Analysis - Abu Dhabi Department of Health",
                xaxis_title="Date",
                yaxis_title="Concentration (LOQ Normalized)",
                showlegend=True
            )
            
            return {
                "success": True,
                "message": "Seasonality analysis completed successfully",
                "data": {
                    "period": request.period,
                    "seasonal_strength": float(seasonal_strength),
                    "trend_strength": float(trend_strength),
                    "seasonal_pattern": seasonal_pattern,
                    "trend_direction": trend_direction,
                    "decomposition_method": request.method,
                    "data_points": len(ts_data),
                    "location_info": {
                        "location": daily_data.iloc[0]['Location'],
                        "gis_id": daily_data.iloc[0]['GIS_ID'],
                        "latitude": float(daily_data.iloc[0]['Latitude']) if pd.notna(daily_data.iloc[0]['Latitude']) else None,
                        "longitude": float(daily_data.iloc[0]['Longitude']) if pd.notna(daily_data.iloc[0]['Longitude']) else None
                    },
                    "data_summary": {
                        "total_records": len(df),
                        "unique_locations": df['Location'].nunique(),
                        "unique_gis_ids": df['GIS_ID'].nunique(),
                        "date_range": {
                            "start": df['SAMPLED_DATE'].min().isoformat(),
                            "end": df['SAMPLED_DATE'].max().isoformat()
                        }
                    }
                },
                "metadata": {
                    "period": request.period,
                    "method": request.method,
                    "filters_applied": request.dict(),
                    "timestamp": datetime.now().isoformat()
                },
                "plot_data": create_plotly_json(fig)
            }
        
        except Exception as decomp_error:
            logger.warning(f"Seasonal decomposition failed, using simple analysis: {decomp_error}")
            
            # Simple seasonal analysis
            df['month'] = df['SAMPLED_DATE'].dt.month
            monthly_avg = df.groupby('month')['concentration_loq_normalized'].mean()
            
            seasonal_pattern = {str(month): float(avg) for month, avg in monthly_avg.items()}
            
            return {
                "success": True,
                "message": "Seasonality analysis completed successfully (simple method)",
                "data": {
                    "period": 12,
                    "seasonal_strength": 0.5,
                    "trend_strength": 0.5,
                    "seasonal_pattern": seasonal_pattern,
                    "trend_direction": "unknown",
                    "decomposition_method": "simple_monthly",
                    "data_points": len(daily_data),
                    "location_info": {
                        "location": daily_data.iloc[0]['Location'],
                        "gis_id": daily_data.iloc[0]['GIS_ID'],
                        "latitude": float(daily_data.iloc[0]['Latitude']) if pd.notna(daily_data.iloc[0]['Latitude']) else None,
                        "longitude": float(daily_data.iloc[0]['Longitude']) if pd.notna(daily_data.iloc[0]['Longitude']) else None
                    },
                    "data_summary": {
                        "total_records": len(df),
                        "unique_locations": df['Location'].nunique(),
                        "unique_gis_ids": df['GIS_ID'].nunique(),
                        "date_range": {
                            "start": df['SAMPLED_DATE'].min().isoformat(),
                            "end": df['SAMPLED_DATE'].max().isoformat()
                        }
                    }
                },
                "metadata": {
                    "period": 12,
                    "method": "simple_monthly",
                    "filters_applied": request.dict(),
                    "timestamp": datetime.now().isoformat()
                },
                "plot_data": "{}"
            }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Seasonality analysis failed: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Seasonality analysis failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
