"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fastify_1 = __importDefault(require("fastify"));
const swagger_1 = __importDefault(require("@fastify/swagger"));
const swagger_ui_1 = __importDefault(require("@fastify/swagger-ui"));
const routes_1 = require("./routes");
const buildApp = async () => {
    const fastify = (0, fastify_1.default)({ logger: true });
    // Register Swagger
    await fastify.register(swagger_1.default, {
        swagger: {
            info: {
                title: "Fastify API",
                description: "API documentation",
                version: "1.0.0",
            },
            host: "localhost:3000",
            schemes: ["http"],
            consumes: ["application/json"],
            produces: ["application/json"],
        },
    });
    // Register Swagger UI
    await fastify.register(swagger_ui_1.default, {
        routePrefix: "/docs",
        uiConfig: {
            docExpansion: "full",
            deepLinking: false,
        },
    });
    // Example route using TypeBox
    //   fastify.get(
    //     "/hello",
    //     {
    //       schema: {
    //         description: "Returns a greeting",
    //         tags: ["Example"],
    //         summary: "Hello endpoint",
    //         response: {
    //           200: Type.Object({
    //             msg: Type.String(),
    //           }),
    //         },
    //       },
    //     },
    //     async () => {
    //       return { msg: "Hello Fastify!" };
    //     }
    //   );
    // Register all modular routes
    await (0, routes_1.registerRoutes)(fastify);
    await fastify.ready();
    fastify.swagger();
    await fastify.listen({ port: 8080 });
};
buildApp().catch(console.error);
