"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const typebox_1 = require("@sinclair/typebox");
const summaryRoute = async (fastify) => {
    const ParamsSchema = typebox_1.Type.Object({
        level: typebox_1.Type.String({
            enum: ["mid", "high", "tech"],
        }),
    });
    const QuerySchema = typebox_1.Type.Object({
        start_date: typebox_1.Type.Optional(typebox_1.Type.String({ format: "date" })),
        end_date: typebox_1.Type.Optional(typebox_1.Type.String({ format: "date" })),
        cards: typebox_1.Type.Optional(typebox_1.Type.String({
            description: "Comma-separated list of card IDs (1,2,3)",
        })),
    });
    fastify.get("/summary/:level", {
        schema: {
            tags: ["Summary"],
            summary: "Get summary cards by level",
            params: ParamsSchema,
            querystring: QuerySchema,
        },
    }, async (request) => {
        const { level } = request.params;
        const { start_date, end_date } = request.query;
        return {
            cards: [
                {
                    id: 1,
                    title: "Risks",
                    type: "two plots card",
                    active: 167,
                    forecast: 200,
                    change: 5.7,
                    uncertainty: 14,
                    direction: "up",
                    list: [
                        {
                            title: "Health",
                            type: "two plots card",
                            active: 892,
                            forecast: 1000,
                            change: 8.3,
                            uncertainty: 67,
                            direction: "up",
                        },
                        {
                            title: "Environmental",
                            type: "two plots card",
                            active: 892,
                            forecast: 1000,
                            change: 8.3,
                            uncertainty: 67,
                            direction: "up",
                        },
                        {
                            title: "Security",
                            type: "two plots card",
                            active: 892,
                            forecast: 1000,
                            change: 8.3,
                            uncertainty: 67,
                            direction: "up",
                        },
                    ],
                },
                {
                    id: 2,
                    title: "Trending Risks",
                    type: "two plots card",
                    active: 892,
                    forecast: 1000,
                    change: 8.3,
                    uncertainty: 67,
                    direction: "down",
                    list: [
                        {
                            title: "Health",
                            type: "two plots card",
                            active: 892,
                            forecast: 1000,
                            change: 8.3,
                            uncertainty: 67,
                            direction: "up",
                        },
                        {
                            title: "Environmental",
                            type: "two plots card",
                            active: 892,
                            forecast: 1000,
                            change: 8.3,
                            uncertainty: 67,
                            direction: "up",
                        },
                        {
                            title: "Security",
                            type: "two plots card",
                            active: 892,
                            forecast: 1000,
                            change: 8.3,
                            uncertainty: 67,
                            direction: "up",
                        },
                    ],
                },
                {
                    id: 3,
                    title: "Radioactivity Risks",
                    type: "two plots card",
                    active: 456,
                    forecast: 600,
                    change: 0,
                    uncertainty: 28,
                    direction: "neutral",
                },
                {
                    id: 4,
                    title: "Risk Regions",
                    type: "two plots card",
                    active: 10,
                    forecast: 12,
                    change: 12.5,
                    uncertainty: 4,
                    direction: "up",
                },
            ],
        };
    });
};
exports.default = summaryRoute;
