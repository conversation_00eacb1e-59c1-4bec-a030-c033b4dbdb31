"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerRoutes = registerRoutes;
const dashboard_1 = __importDefault(require("./dashboard"));
async function registerRoutes(fastify) {
    fastify.register(dashboard_1.default, { prefix: "/dashboard" });
}
