

1)  Phylum 




ALTER PROCEDURE [PowerBI].[spGetPhylum]   
    @StartDate DATE,   
    @EndDate DATE,
	@Result_Name VARCHAR(100) = NULL,
	@Location VARCHAR(100) = NULL
AS   

SET NOCOUNT ON;  
SELECT DISTINCT sa.text_id As Text_ID,
        sp.Community AS Collection_Point,
        sp.Location,
        --sp.c_coordinates AS Coordinates,
        sp.Latitude,
        sp.Longitude,
		sp.GISID,
        sa.sampled_date As Date, 
		p.sample_name as sample_id,
		m.organism_name as result_name,
        p.count as result_value,
		p.percentage,
		PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY p.count)
						OVER (PARTITION BY m.organism_name) AS Median

FROM  bix.phylum as p
INNER JOIN lims.sample sa
		ON sa.text_id= p.sample_name
	   AND sa.TEMPLATE = 'WWML'
INNER JOIN lims.sampling_points sp 
		ON sp.GISID = sa.C_GISID
INNER JOIN bix.mapping as m
        on p.tax_id = m.tax_id
	 where (sa.sampled_date >= @StartDate AND sa.sampled_date <= @EndDate)
	 AND (@Result_Name IS NULL OR m.organism_name LIKE '%'+@Result_Name+'%' OR m.organism_name=@Result_Name) 
	 AND (@Location IS NULL OR sp.location LIKE '%'+@Location+'%')
	 AND p.final_result = 1;


DUCK DB SQL:

SELECT DISTINCT 
    sa.TEXT_ID AS Text_ID,
    sp.Community AS Collection_Point,
    sp.Location,
    sp.Latitude,
    sp.Longitude,
    sp.GISID,
    sa.SAMPLED_DATE AS Date, 
    p.sample_name AS sample_id,
    m.organism_name AS result_name,
    p.count AS result_value,
    p.percentage,
    quantile_cont(p.count, 0.5) 
        OVER (PARTITION BY m.organism_name) AS Median
FROM bix_phylum AS p
INNER JOIN sample sa
    ON sa.TEXT_ID = p.sample_name
   AND sa.TEMPLATE = 'WWML'
INNER JOIN sampling_points sp
    ON sp.GISID = sa.c_gisid
INNER JOIN bix_mapping m
    ON p.tax_id = m.tax_id
WHERE p.final_result = 1;


2) Species 



USE [G42HealthcareWWDMP]
GO
/****** Object:  StoredProcedure [PowerBI].[spGetSpecies]    Script Date: 8/26/2025 12:41:28 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


ALTER PROCEDURE [PowerBI].[spGetSpecies]   
    @StartDate DATE,   
    @EndDate DATE ,
	@Result_Name VARCHAR(100) = NULL,
	@Location VARCHAR(100) = NULL
AS   

SET NOCOUNT ON;  
SELECT  DISTINCT sa.text_id As Text_ID,
        sp.Community,
        sp.Location,
		sp.latitude,
		sp.longitude,
		sp.GISID,
        sa.sampled_date As Date, 
        s.sample_name as sample_id,
		s.tax_id,
        m.organism_name as result_name,
		m.organism_level,
        s.count as result_value,
		sa.SAMPLE_NUMBER AS SampleNumber,
		s.percentage,
		PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY s.count)
						OVER (PARTITION BY m.organism_name) AS Median
	
FROM bix.species as s
INNER JOIN  lims.sample sa ON sa.text_id= s.sample_name
INNER JOIN  lims.sampling_points sp ON sp.GISID = sa.C_GISID
INNER JOIN  bix.mapping as m on s.tax_id = m.tax_id
where (sa.sampled_date >= @StartDate AND sa.sampled_date <= @EndDate)
	  AND (@Result_Name IS NULL OR m.organism_name LIKE '%'+@Result_Name+'%' OR m.organism_name=@Result_Name)
	   AND (@Location IS NULL OR sp.location LIKE '%'+@Location+'%')
	   AND s.final_result = 1
	   AND sa.template = 'WWML'
	   AND sa.c_gis_id is not null 
	   AND sp.location is not null


DUCK DB SQL :

SELECT DISTINCT 
    sa.TEXT_ID AS Text_ID,
    sp.Community,
    sp.Location,
    sp.Latitude,
    sp.Longitude,
    sp.GISID,
    sa.SAMPLED_DATE AS Date, 
    s.sample_name AS sample_id,
    s.tax_id,
    m.organism_name AS result_name,
    m.organism_level,
    s.count AS result_value,
    sa.SAMPLE_NUMBER AS SampleNumber,
    s.percentage,
    quantile_cont(s.count, 0.5) 
        OVER (PARTITION BY m.organism_name) AS Median
FROM bix_species AS s
INNER JOIN sample sa 
    ON sa.TEXT_ID = s.sample_name
INNER JOIN sampling_points sp 
    ON sp.GISID = sa.c_gisid
INNER JOIN bix_mapping m 
    ON s.tax_id = m.tax_id
WHERE s.final_result = 1
  AND sa.TEMPLATE = 'WWML'
  AND sa.c_gisid IS NOT NULL
  AND sp.Location IS NOT NULL;

-----
3) Genus 

 
ALTER PROCEDURE [PowerBI].[spGetGenus]   
    @StartDate DATE,   
    @EndDate DATE,
	@Result_Name VARCHAR(100) = NULL,
	@Location VARCHAR(100) = NULL
AS   

SET NOCOUNT ON;  

SELECT DISTINCT sa.text_id As Text_ID,
        sp.Community AS Collection_Point,
        sp.Location,
        --sp.c_coordinates AS Coordinates,
		sp.GISID,
        sp.Latitude,
        sp.Longitude,
        sa.sampled_date As Date, 
		g.sample_name as sample_id,
		m.organism_name as result_name,
        g.count as result_value,
		g.percentage,
		PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY g.count)
						OVER (PARTITION BY m.organism_name) AS Median

FROM  bix.genus as g
INNER JOIN lims.sample sa
		ON sa.text_id= g.sample_name
	   AND sa.TEMPLATE = 'WWML'
INNER JOIN lims.sampling_points sp 
		ON sp.GISID = sa.C_GISID
INNER JOIN bix.mapping as m
        on g.tax_id = m.tax_id
		where (sa.sampled_date >= @StartDate AND sa.sampled_date <= @EndDate)
		AND (@Result_Name IS NULL OR m.organism_name LIKE '%'+@Result_Name+'%' OR m.organism_name=@Result_Name)
		AND (@Location IS NULL OR sp.location LIKE '%'+@Location+'%')
		and g.final_result = 1

DUCK DB SQL:
SELECT DISTINCT 
    m.organism_name AS result_name,
    sa.TEXT_ID AS Text_ID,
    sp.Community AS Collection_Point,
    sp.Location,
    sp.GISID,
    sp.Latitude,
    sp.Longitude,
    sa.SAMPLED_DATE AS Date, 
    g.sample_name AS sample_id,
    g.count AS result_value,
    g.percentage,
    quantile_cont(g.count, 0.5) 
        OVER (PARTITION BY m.organism_name) AS Median
FROM bix_genus AS g
INNER JOIN sample sa
    ON sa.TEXT_ID = g.sample_name
   AND sa.TEMPLATE = 'WWML'
INNER JOIN sampling_points sp 
    ON sp.GISID = sa.c_gisid
INNER JOIN bix_mapping m
    ON g.tax_id = m.tax_id
WHERE g.final_result = 1;





SELECT DISTINCT 
    m.organism_name AS result_name,
    sa.text_id AS Text_ID,
    sp.Community AS Collection_Point,
    sp.Location,
    sp.GISID,
    sp.Latitude,
    sp.Longitude,
    sa.sampled_date AS Date, 
    g.sample_name AS sample_id,
    g.count AS result_value,
    g.percentage,
    quantile_cont(g.count, 0.5) 
        OVER (PARTITION BY m.organism_name) AS Median
FROM bix_genus AS g
INNER JOIN sample sa
    ON sa.text_id = g.sample_name
   AND sa.template = 'WWML'
INNER JOIN sampling_points sp 
    ON sp.GISID = sa.c_gisid
INNER JOIN bix_mapping m
    ON g.tax_id = m.tax_id
WHERE (sa.sampled_date >= $StartDate AND sa.sampled_date <= $EndDate)
  AND (result_name IS NULL 
       OR m.organism_name ILIKE '%' || result_name || '%'
       OR m.organism_name = result_name)
  AND ($Location IS NULL 
       OR sp.Location ILIKE '%' || $Location || '%')
  AND g.final_result = 1;
