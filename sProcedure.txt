

1)  Phylum 




ALTER PROCEDURE [PowerBI].[spGetPhylum]   
    @StartDate DATE,   
    @EndDate DATE,
	@Result_Name VARCHAR(100) = NULL,
	@Location VARCHAR(100) = NULL
AS   

SET NOCOUNT ON;  
SELECT DISTINCT sa.text_id As Text_ID,
        sp.Community AS Collection_Point,
        sp.Location,
        --sp.c_coordinates AS Coordinates,
        sp.Latitude,
        sp.Longitude,
		sp.GISID,
        sa.sampled_date As Date, 
		p.sample_name as sample_id,
		m.organism_name as result_name,
        p.count as result_value,
		p.percentage,
		PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY p.count)
						OVER (PARTITION BY m.organism_name) AS Median

FROM  bix.phylum as p
INNER JOIN lims.sample sa
		ON sa.text_id= p.sample_name
	   AND sa.TEMPLATE = 'WWML'
INNER JOIN lims.sampling_points sp 
		ON sp.GISID = sa.C_GISID
INNER JOIN bix.mapping as m
        on p.tax_id = m.tax_id
	 where (sa.sampled_date >= @StartDate AND sa.sampled_date <= @EndDate)
	 AND (@Result_Name IS NULL OR m.organism_name LIKE '%'+@Result_Name+'%' OR m.organism_name=@Result_Name) 
	 AND (@Location IS NULL OR sp.location LIKE '%'+@Location+'%')
	 AND p.final_result = 1;


2) Species 



USE [G42HealthcareWWDMP]
GO
/****** Object:  StoredProcedure [PowerBI].[spGetSpecies]    Script Date: 8/26/2025 12:41:28 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


ALTER PROCEDURE [PowerBI].[spGetSpecies]   
    @StartDate DATE,   
    @EndDate DATE ,
	@Result_Name VARCHAR(100) = NULL,
	@Location VARCHAR(100) = NULL
AS   

SET NOCOUNT ON;  
SELECT  DISTINCT sa.text_id As Text_ID,
        sp.Community,
        sp.Location,
		sp.latitude,
		sp.longitude,
		sp.GISID,
        sa.sampled_date As Date, 
        s.sample_name as sample_id,
		s.tax_id,
        m.organism_name as result_name,
		m.organism_level,
        s.count as result_value,
		sa.SAMPLE_NUMBER AS SampleNumber,
		s.percentage,
		PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY s.count)
						OVER (PARTITION BY m.organism_name) AS Median
	
FROM bix.species as s
INNER JOIN  lims.sample sa ON sa.text_id= s.sample_name
INNER JOIN  lims.sampling_points sp ON sp.GISID = sa.C_GISID
INNER JOIN  bix.mapping as m on s.tax_id = m.tax_id
where (sa.sampled_date >= @StartDate AND sa.sampled_date <= @EndDate)
	  AND (@Result_Name IS NULL OR m.organism_name LIKE '%'+@Result_Name+'%' OR m.organism_name=@Result_Name)
	   AND (@Location IS NULL OR sp.location LIKE '%'+@Location+'%')
	   AND s.final_result = 1
	   AND sa.template = 'WWML'
	   AND sa.c_gis_id is not null 
	   AND sp.location is not null



3) Genus 

 
ALTER PROCEDURE [PowerBI].[spGetGenus]   
    @StartDate DATE,   
    @EndDate DATE,
	@Result_Name VARCHAR(100) = NULL,
	@Location VARCHAR(100) = NULL
AS   

SET NOCOUNT ON;  

SELECT DISTINCT sa.text_id As Text_ID,
        sp.Community AS Collection_Point,
        sp.Location,
        --sp.c_coordinates AS Coordinates,
		sp.GISID,
        sp.Latitude,
        sp.Longitude,
        sa.sampled_date As Date, 
		g.sample_name as sample_id,
		m.organism_name as result_name,
        g.count as result_value,
		g.percentage,
		PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY g.count)
						OVER (PARTITION BY m.organism_name) AS Median

FROM  bix.genus as g
INNER JOIN lims.sample sa
		ON sa.text_id= g.sample_name
	   AND sa.TEMPLATE = 'WWML'
INNER JOIN lims.sampling_points sp 
		ON sp.GISID = sa.C_GISID
INNER JOIN bix.mapping as m
        on g.tax_id = m.tax_id
		where (sa.sampled_date >= @StartDate AND sa.sampled_date <= @EndDate)
		AND (@Result_Name IS NULL OR m.organism_name LIKE '%'+@Result_Name+'%' OR m.organism_name=@Result_Name)
		AND (@Location IS NULL OR sp.location LIKE '%'+@Location+'%')
		and g.final_result = 1
