select
    da.persona,
    a."labName" as analyte_category,
    a.analyte_group,
    a.name as analyte_name,
    a.sample_type,
    a.source_data_flag,
    a."Analyte_Group",
    a."Lab"
from
    summary_results_unified a
    inner join dim_analyte da on da.name = a.name
where
    1 = 1
group by
    da.persona,
    a."labName",
    a.analyte_group,
    a.name,
    a.sample_type,
    a.source_data_flag,
    a."Analyte_Group",
    a."Lab";

--- 
--- 
--- 
select
    a."labName" as analyte_category
from
    summary_results_unified a
    inner join dim_analyte da on da.name = a.name
where
    1 = 1
group by
    a."labName";

SELECT
    "SAMPLE_NUMBER",
    DATE_TRUNC('month', "sampled_date") AS time_period,
    "GIS_ID" AS GIS_ID,
    CASE
        WHEN 'month' = 'week' THEN strftime(DATE_TRUNC('month', "sampled_date"), '%Y-W%V')
        WHEN 'month' = 'month' THEN strftime(DATE_TRUNC('month', "sampled_date"), '%Y-%m')
        WHEN 'month' = 'quarter' THEN strftime(DATE_TRUNC('month', "sampled_date"), '%Y') || '-Q' || CAST(
            (
                (
                    CAST(
                        strftime('%m', DATE_TRUNC('month', "sampled_date")) AS INTEGER
                    ) - 1
                ) / 3 + 1
            ) AS VARCHAR
        )
        WHEN 'month' = 'year' THEN strftime(DATE_TRUNC('month', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('month', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
    summary_results_unified
WHERE
    1 = 1
    AND (
        "sampled_date" BETWEEN '2023-12-31'
        AND '2025-08-03'
    )
    AND "name" = 'Tramadol'
    AND "analyte_group" = 'ILLICIT_DRUGS'
    AND "labName" = 'Organic Chemistry'
GROUP BY
    "SAMPLE_NUMBER",
    GIS_ID,
    DATE_TRUNC('month', "sampled_date")
ORDER BY
    time_period ASC;

SELECT
    labName
from
    summary_results_unified
where
    "labName" like '%Chemistry%'
group by
    labName