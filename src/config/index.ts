import { query } from "../utils/db";
import { query as queryDuck } from "../utils/duckdb";

import {
  calculateComparisonDateRange,
  capPercentageChange,
} from "../utils/utils";

export const allWidgets = {
  sample_overview_cards: {
    "1": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
                SELECT
                  count(distinct "SAMPLE_NUMBER") ${
                    start_date && end_date
                      ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
                      : ""
                  } as period_samples,
                  COUNT(DISTINCT "SAMPLE_NUMBER") ${
                    comparisonDates?.comparison_start_date &&
                    comparisonDates?.comparison_end_date
                      ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                      : ""
                  } as comparison_samples
                FROM
                    summary_results_unified
                WHERE 1=1
                ${
                  location
                    ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
                    : ""
                }
                ${analyte ? `AND "name" = '${analyte}'` : ""}
                ${group ? `AND "analyte_group" = '${group}'` : ""}
                ${category ? `AND "labName" = '${category}'` : ""}
                ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
               ;
         `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};

        let percentageChange = 0;
        let hasComparison = false;

        if (
          start_date &&
          end_date &&
          comparisonDates &&
          comparisonValue &&
          comparisonValue > 0
        ) {
          percentageChange =
            (((currentValue || 0) - comparisonValue) / comparisonValue) * 100;
          hasComparison = true;
        }

        const finalValue = currentValue || 0;
        const finalPct = hasComparison
          ? capPercentageChange(percentageChange)
          : 0;
        const finalDirection = hasComparison
          ? percentageChange > 0
            ? "up"
            : percentageChange < 0
            ? "down"
            : "center"
          : "center";

        return {
          id: 1,
          name: "Samples Collected",
          type: "single_number",
          value: finalValue,
          change: {
            pct: finalPct,
            direction: finalDirection,
            prefix: "%",
          },
          legend: "Change",
        };
      },
      info: {
        id: 1,
        name: "Total Samples Collected",
      },
    },
    "2": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
                SELECT
                  count(distinct "SAMPLE_NUMBER") ${
                    start_date && end_date
                      ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
                      : ""
                  } as period_samples,
                  COUNT(DISTINCT "SAMPLE_NUMBER") ${
                    comparisonDates?.comparison_start_date &&
                    comparisonDates?.comparison_end_date
                      ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                      : ""
                  } as comparison_samples
                FROM
                   summary_results_unified
                WHERE 1=1
               and "ResultStatus" = 'A'
              
                ${
                  location
                    ? `AND ("CatchmentArea" = '${location}'  OR "GIS_ID" = '${location}')`
                    : ""
                }
                ${analyte ? `AND "name" = '${analyte}'` : ""}
                ${group ? `AND "analyte_group" = '${group}'` : ""}
                ${category ? `AND "labName" = '${category}'` : ""}
                ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
               
         `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};

        let percentageChange = 0;
        let hasComparison = false;

        if (
          start_date &&
          end_date &&
          comparisonDates &&
          comparisonValue &&
          comparisonValue > 0
        ) {
          percentageChange =
            (((currentValue || 0) - comparisonValue) / comparisonValue) * 100;
          hasComparison = true;
        }

        const finalValue = currentValue || 0;
        const finalPct = hasComparison
          ? capPercentageChange(percentageChange)
          : 0;
        const finalDirection = hasComparison
          ? percentageChange > 0
            ? "up"
            : percentageChange < 0
            ? "down"
            : "center"
          : "center";

        return {
          id: 2,
          name: "Samples Tested",
          type: "single_number",
          value: currentValue,
          change: {
            pct: capPercentageChange(percentageChange),
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Tested Samples",
        };
      },
      info: {
        id: 2,
        name: "Total Samples Tested",
        description: "Total number of samples that have been tested",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "3": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
                SELECT
                  count(distinct "location") ${
                    start_date && end_date
                      ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
                      : ""
                  } as period_samples,
                  COUNT(DISTINCT "location") ${
                    comparisonDates?.comparison_start_date &&
                    comparisonDates?.comparison_end_date
                      ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                      : ""
                  } as comparison_samples
                FROM
                  summary_results_unified
                WHERE 1=1
                ${
                  location
                    ? `AND ("CatchmentArea" = '${location}'  OR "GIS_ID" = '${location}')`
                    : ""
                }
                ${analyte ? `AND "name" = '${analyte}'` : ""}
                ${group ? `AND "analyte_group" = '${group}'` : ""}
                ${category ? `AND "labName" = '${category}'` : ""}
                ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
               
         `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};

        let percentageChange = 0;
        let hasComparison = false;

        if (
          start_date &&
          end_date &&
          comparisonDates &&
          comparisonValue &&
          comparisonValue > 0
        ) {
          percentageChange =
            (((currentValue || 0) - comparisonValue) / comparisonValue) * 100;
          hasComparison = true;
        }

        const finalValue = currentValue || 0;
        const finalPct = hasComparison
          ? capPercentageChange(percentageChange)
          : 0;
        const finalDirection = hasComparison
          ? percentageChange > 0
            ? "up"
            : percentageChange < 0
            ? "down"
            : "center"
          : "center";

        return {
          id: 3,
          name: "Number of Collection Points",
          type: "single_number",
          value: finalValue,
          change: {
            pct: finalPct,
            direction: finalDirection,
            prefix: "%",
          },
          legend: "Active Points",
        };
      },
      info: {
        id: 3,
        name: "Total Number of Collection Points",
        description: "Total number of active sample collection points",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "4": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
                SELECT
                  count(distinct "sampled_date") ${
                    start_date && end_date
                      ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
                      : ""
                  } as period_samples ,
                  COUNT(DISTINCT "sampled_date") ${
                    comparisonDates?.comparison_start_date &&
                    comparisonDates?.comparison_end_date
                      ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                      : ""
                  } as comparison_samples
                FROM
                    summary_results_unified
                WHERE 1=1
                ${
                  location
                    ? `AND ("CatchmentArea" = '${location}'  OR "GIS_ID" = '${location}')`
                    : ""
                }
                ${analyte ? `AND "name" = '${analyte}'` : ""}
                ${group ? `AND "analyte_group" = '${group}'` : ""}
                ${category ? `AND "labName" = '${category}'` : ""}
                ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
               
         `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};

        let percentageChange = 0;
        let hasComparison = false;

        if (
          start_date &&
          end_date &&
          comparisonDates &&
          comparisonValue &&
          comparisonValue > 0
        ) {
          percentageChange =
            (((currentValue || 0) - comparisonValue) / comparisonValue) * 100;
          hasComparison = true;
        }

        const finalValue = currentValue || 0;
        const finalPct = hasComparison
          ? capPercentageChange(percentageChange)
          : 0;
        const finalDirection = hasComparison
          ? percentageChange > 0
            ? "up"
            : percentageChange < 0
            ? "down"
            : "center"
          : "center";

        return {
          id: 4,
          name: "Days of Sample Collection",
          type: "single_number",
          value: finalValue,
          change: {
            pct: finalPct,
            direction: finalDirection,
            prefix: "%",
          },
          legend: "Collection Days",
        };
      },
      info: {
        id: 4,
        name: "Total Days of Sample Collection",
        description: "Total number of days with sample collection activity",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    // "5": {
    //   // this is unused
    //   get: async (params: any) => {
    //     const {
    //       start_date,
    //       end_date,
    //       analyte,
    //       category,
    //       group,
    //       location,
    //       sample_type,
    //     } = params;

    //     const comparisonDates =
    //       start_date && end_date
    //         ? calculateComparisonDateRange(start_date, end_date)
    //         : null;

    //     const getDateFilter = (field: string) => {
    //       if (start_date && end_date)
    //         return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
    //       if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
    //       if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
    //       return "";
    //     };

    //     const getWhereDate = () => {
    //       if (start_date && end_date)
    //         return `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`;
    //       if (start_date) return `AND (ds.date_collected >= '${start_date}')`;
    //       if (end_date) return `AND (ds.date_collected <= '${end_date}')`;
    //       return "";
    //     };

    //     const res = await query(`
    //     SELECT
    //       fts.data_source,
    //       COUNT(DISTINCT fts.sample_key) as sample_count,
    //       COUNT(DISTINCT fts.sample_key) ${getDateFilter(
    //         "ds.date_collected"
    //       )} as period_count,
    //       COUNT(DISTINCT fts.sample_key) ${
    //         comparisonDates?.comparison_start_date &&
    //         comparisonDates?.comparison_end_date
    //           ? `FILTER (WHERE ds.date_collected BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
    //           : ""
    //       } as comparison_count
    //     FROM ${cards_query_info.sample_card.table}
    //         ${cards_query_info.sample_card.join}
    //     WHERE 1=1
    //     ${cards_query_info.sample_card.params({
    //       analyte,
    //       category,
    //       group,
    //       sample_type,
    //       location,
    //     })}

    //         GROUP BY fts.data_source
    //     ORDER BY sample_count DESC
    //   `);

    //     const totalCount = res.reduce((sum, row) => sum + row.period_count, 0);
    //     const comparisonCount = res.reduce(
    //       (sum, row) => sum + row.comparison_count,
    //       0
    //     );
    //     const percentageChange =
    //       comparisonCount > 0
    //         ? ((totalCount - comparisonCount) / comparisonCount) * 100
    //         : 0;

    //     return {
    //       id: 5,
    //       name: "Routine and Ad-hoc Samples Tested",
    //       type: "single_number",
    //       value: totalCount,
    //       change: {
    //         pct: capPercentageChange(percentageChange),
    //         direction:
    //           percentageChange > 0
    //             ? "up"
    //             : percentageChange < 0
    //             ? "down"
    //             : "center",
    //         prefix: "%",
    //       },
    //       legend: "Routine vs Ad-hoc",
    //     };
    //   },
    //   info: {
    //     id: 5,
    //     name: "Routine and Ad-hoc Samples Tested",
    //     description: "Total samples tested by data source type",
    //     category: "Sample Overview",
    //     parameters: [
    //       "level",
    //       "start_date",
    //       "end_date",
    //       "analyte",
    //       "category",
    //       "group",
    //       "location",
    //       "sample_type",
    //     ],
    //   },
    // },
    "6": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
                SELECT
                  count(distinct "SAMPLE_NUMBER") ${
                    start_date && end_date
                      ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
                      : ""
                  } as period_samples,
                  COUNT(DISTINCT "SAMPLE_NUMBER") ${
                    comparisonDates?.comparison_start_date &&
                    comparisonDates?.comparison_end_date
                      ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                      : ""
                  } as comparison_samples
                FROM
                    summary_results_unified
                WHERE 1=1
                and "SampleStatus" not in ('U')
                ${
                  location
                    ? `AND ("CatchmentArea" = '${location}'  OR "GIS_ID" = '${location}')`
                    : ""
                }
                ${analyte ? `AND "name" = '${analyte}'` : ""}
                ${group ? `AND "analyte_group" = '${group}'` : ""}
                ${category ? `AND "labName" = '${category}'` : ""}
                ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}    
              
         `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};

        let percentageChange = 0;
        let hasComparison = false;

        if (
          start_date &&
          end_date &&
          comparisonDates &&
          comparisonValue &&
          comparisonValue > 0
        ) {
          percentageChange =
            (((currentValue || 0) - comparisonValue) / comparisonValue) * 100;
          hasComparison = true;
        }

        const finalValue = currentValue || 0;
        const finalPct = hasComparison
          ? capPercentageChange(percentageChange)
          : 0;
        const finalDirection = hasComparison
          ? percentageChange > 0
            ? "up"
            : percentageChange < 0
            ? "down"
            : "center"
          : "center";

        return {
          id: 6,
          name: "Number of Samples Received",
          type: "single_number",
          value: finalValue,
          change: {
            pct: finalPct,
            direction: finalDirection,
            prefix: "%",
          },
          legend: "Collected Samples",
        };
      },
      info: {
        id: 6,
        name: "Total Number of Samples Received ",
        description: "Total Number of Samples Received  (with date_collected)",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "7": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
                SELECT
                  count(distinct "SAMPLE_NUMBER") ${
                    start_date && end_date
                      ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
                      : ""
                  } as period_samples,
                  COUNT(DISTINCT "SAMPLE_NUMBER") ${
                    comparisonDates?.comparison_start_date &&
                    comparisonDates?.comparison_end_date
                      ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                      : ""
                  } as comparison_samples
                FROM
                    summary_results_unified
                WHERE 1=1
                and "SampleStatus" = 'I'
                ${
                  location
                    ? `AND ("CatchmentArea" = '${location}'  OR "GIS_ID" = '${location}')`
                    : ""
                }
                ${analyte ? `AND "name" = '${analyte}'` : ""}
                ${group ? `AND "analyte_group" = '${group}'` : ""}
                ${category ? `AND "labName" = '${category}'` : ""}
                ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
               
         `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};

        let percentageChange = 0;
        let hasComparison = false;

        if (
          start_date &&
          end_date &&
          comparisonDates &&
          comparisonValue &&
          comparisonValue > 0
        ) {
          percentageChange =
            (((currentValue || 0) - comparisonValue) / comparisonValue) * 100;
          hasComparison = true;
        }

        const finalValue = currentValue || 0;
        const finalPct = hasComparison
          ? capPercentageChange(percentageChange)
          : 0;
        const finalDirection = hasComparison
          ? percentageChange > 0
            ? "up"
            : percentageChange < 0
            ? "down"
            : "center"
          : "center";

        return {
          id: 7,
          name: "Samples to Test",
          type: "single_number",
          value: finalValue,
          change: {
            pct: finalPct,
            direction: finalDirection,
            prefix: "%",
          },
          legend: "Incomplete Sample",
        };
      },
      info: {
        id: 7,
        name: "Samples to Test",
        description: "Total samples with status 'Incomplete' testing",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "8": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
                SELECT
                  count(distinct "TEST_NUMBER") ${
                    start_date && end_date
                      ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
                      : ""
                  } as period_samples,
                  COUNT(DISTINCT "TEST_NUMBER") ${
                    comparisonDates?.comparison_start_date &&
                    comparisonDates?.comparison_end_date
                      ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                      : ""
                  } as comparison_samples
                FROM
                    summary_results_unified
                WHERE 1=1
                and "finalresult_value" =  'Detected'
                ${
                  location
                    ? `AND ("CatchmentArea" = '${location}'  OR "GIS_ID" = '${location}')`
                    : ""
                }
                ${analyte ? `AND "name" = '${analyte}'` : ""}
                ${group ? `AND "analyte_group" = '${group}'` : ""}
                ${category ? `AND "labName" = '${category}'` : ""}
                ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
                
         `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};

        let percentageChange = 0;
        let hasComparison = false;

        if (
          start_date &&
          end_date &&
          comparisonDates &&
          comparisonValue &&
          comparisonValue > 0
        ) {
          percentageChange =
            (((currentValue || 0) - comparisonValue) / comparisonValue) * 100;
          hasComparison = true;
        }

        const finalValue = currentValue || 0;
        const finalPct = hasComparison
          ? capPercentageChange(percentageChange)
          : 0;
        const finalDirection = hasComparison
          ? percentageChange > 0
            ? "up"
            : percentageChange < 0
            ? "down"
            : "center"
          : "center";

        return {
          id: 8,
          name: "Samples Tested Positive",
          type: "single_number",
          value: finalValue,
          change: {
            pct: finalPct,
            direction: finalDirection,
            prefix: "%",
          },
          legend: "Detected Samples",
        };
      },
      info: {
        id: 8,
        name: "Samples Tested Positive",
        description: "Samples with 'Detected' analytes",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    // SECTION: under not done
    "9": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
          SELECT 
            count(distinct "SAMPLE_NUMBER") ${
              start_date && end_date
                ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
                : ""
            } as period_samples,
            COUNT(DISTINCT "SAMPLE_NUMBER") ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples

          FROM
            summary_results_unified
            WHERE 1=1
              and "finalresult_value" =  'Not Detected'
              and "ResultStatus" = 'A'
              ${
                location
                  ? `AND ("CatchmentArea" = '${location}'  OR "GIS_ID" = '${location}')`
                  : ""
              }
              ${analyte ? `AND "name" = '${analyte}'` : ""}
              ${group ? `AND "analyte_group" = '${group}'` : ""}
              ${category ? `AND "labName" = '${category}'` : ""}
              ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 9,
          name: "Samples Tested Negative",
          type: "single_number",
          value: currentValue,
          change: {
            pct: capPercentageChange(percentageChange),
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Not Detected & Unknown",
        };
      },
      info: {
        id: 9,
        name: "Samples Tested Negative",
        description: "Samples with 'Not Detected' or 'Unknown' analytes",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "10": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
        SELECT
          COUNT(*) as samples_not_started,
          count(distinct "SAMPLE_NUMBER") ${
            start_date && end_date
              ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
              : ""
          } as period_samples,
          COUNT(DISTINCT "SAMPLE_NUMBER") ${
            comparisonDates?.comparison_start_date &&
            comparisonDates?.comparison_end_date
              ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
              : ""
          } as comparison_samples
        FROM
          summary_results_unified
          WHERE 1=1
          and "SampleStatus" = 'U'
            ${
              location
                ? `AND ("CatchmentArea" = '${location}'  OR "GIS_ID" = '${location}')`
                : ""
            }
            ${analyte ? `AND "name" = '${analyte}'` : ""}
            ${group ? `AND "analyte_group" = '${group}'` : ""}
            ${category ? `AND "labName" = '${category}'` : ""}
            ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

      `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 10,
          name: "Samples Not Started",
          type: "single_number",
          value: currentValue,
          change: {
            pct: capPercentageChange(percentageChange),
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Backlog",
        };
      },
      info: {
        id: 10,
        name: "Samples Not Started",
        description:
          "Samples that have not been started (no test results) - can only filter by sample_type",
        category: "Sample Overview",
        parameters: ["level", "start_date", "end_date", "sample_type"],
      },
    },
    "11": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
        SELECT
          COUNT(*) as samples_not_tested,
          count(distinct "SAMPLE_NUMBER") ${
            start_date && end_date
              ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
              : ""
          } as period_samples,
          COUNT(DISTINCT "SAMPLE_NUMBER") ${
            comparisonDates?.comparison_start_date &&
            comparisonDates?.comparison_end_date
              ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
              : ""
          } as comparison_samples
        FROM
          summary_results_unified
          WHERE 1=1
            and "TestStatus" = 'I'
            ${
              location
                ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
                : ""
            }
            ${analyte ? `AND "name" = '${analyte}'` : ""}
            ${group ? `AND "analyte_group" = '${group}'` : ""}
            ${category ? `AND "labName" = '${category}'` : ""}
            ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
   
      `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 11,
          name: "Samples Not Tested",
          type: "single_number",
          value: currentValue,
          change: {
            pct: capPercentageChange(percentageChange),
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Cancelled",
        };
      },
      info: {
        id: 11,
        name: "Samples Not Tested",
        description: "Samples with status 'X' (Cancelled)",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "location",
          "sample_type",
        ],
      },
    },
    "12": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
          SELECT 
            count(distinct "SAMPLE_NUMBER") ${
              start_date && end_date
                ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
                : ""
            } as period_samples,
            COUNT(DISTINCT "SAMPLE_NUMBER") ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM
            summary_results_unified
            WHERE 1=1
              and "ResultStatus" = 'A'
              ${
                location
                  ? `AND ("CatchmentArea" = '${location}'  OR "GIS_ID" = '${location}')`
                  : ""
              }
              ${analyte ? `AND "name" = '${analyte}'` : ""}
              ${group ? `AND "analyte_group" = '${group}'` : ""}
              ${category ? `AND "labName" = '${category}'` : ""}
              ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
     
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 12,
          name: "Samples Authorized",
          type: "single_number",
          value: currentValue,
          change: {
            pct: capPercentageChange(percentageChange),
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Finalized",
        };
      },
      info: {
        id: 12,
        name: "Samples Authorized",
        description: "Samples with quality_flag = 'Approved'",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "13": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
        SELECT 
          count(distinct "SAMPLE_NUMBER") ${
            start_date && end_date
              ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
              : ""
          } as period_samples,
          COUNT(DISTINCT "SAMPLE_NUMBER") ${
            comparisonDates?.comparison_start_date &&
            comparisonDates?.comparison_end_date
              ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
              : ""
          } as comparison_samples
        FROM
          summary_results_unified
          WHERE 1=1
            and "ResultStatus" <> 'A'
            ${
              location
                ? `AND ("CatchmentArea" = '${location}'  OR "GIS_ID" = '${location}')`
                : ""
            }
            ${analyte ? `AND "name" = '${analyte}'` : ""}
            ${group ? `AND "analyte_group" = '${group}'` : ""}
            ${category ? `AND "labName" = '${category}'` : ""}
            ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
   
      `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 13,
          name: "Samples Not Authorized",
          type: "single_number",
          value: currentValue,
          change: {
            pct: capPercentageChange(percentageChange),
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Awaiting Review",
        };
      },
      info: {
        id: 13,
        name: "Samples Not Authorized",
      },
    },
    // "14": {
    //   get: async (params: any) => {
    //     const {
    //       start_date,
    //       end_date,
    //       analyte,
    //       category,
    //       group,
    //       location,
    //       sample_type,
    //     } = params;

    //     const comparisonDates =
    //       start_date && end_date
    //         ? calculateComparisonDateRange(start_date, end_date)
    //         : null;

    //     const res = await queryDuck(`
    //     SELECT
    //       COUNT(distinct name) as undetected_analytes,
    //       count(distinct "TEST_NUMBER") ${
    //         start_date && end_date
    //           ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
    //           : ""
    //       } as period_samples,
    //       COUNT(distinct "TEST_NUMBER") ${
    //         comparisonDates?.comparison_start_date &&
    //         comparisonDates?.comparison_end_date
    //           ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
    //           : ""
    //       } as comparison_samples
    //       FROM
    //         summary_results_unified
    //         WHERE 1=1
    //           and "finalresult_value" =  'Not Detected'
    //           and "ResultStatus" = 'A'
    //           ${
    //             location
    //               ? `AND ("CatchmentArea" = '${location}'  OR "GIS_ID" = '${location}')`
    //               : ""
    //           }
    //           ${analyte ? `AND "name" = '${analyte}'` : ""}
    //           ${group ? `AND "analyte_group" = '${group}'` : ""}
    //           ${category ? `AND "labName" = '${category}'` : ""}
    //           ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
    //        `);

    //     const {
    //       period_samples: currentValue,
    //       comparison_samples: comparisonValue,
    //     } = res[0] || {};
    //     const percentageChange =
    //       comparisonValue > 0
    //         ? ((currentValue - comparisonValue) / comparisonValue) * 100
    //         : 0;

    //     return {
    //       id: 14,
    //       name: "Undetected Analytes",
    //       type: "single_number",
    //       value: currentValue,
    //       change: {
    //         pct: capPercentageChange(percentageChange),
    //         direction:
    //           percentageChange > 0
    //             ? "up"
    //             : percentageChange < 0
    //             ? "down"
    //             : "center",
    //         prefix: "%",
    //       },
    //       legend: "<LOD",
    //     };
    //   },
    //   info: {
    //     id: 14,
    //     name: "Undetected Analytes",
    //   },
    // },
    // "15": {
    //   get: async (params: any) => {
    //     const {
    //       start_date,
    //       end_date,
    //       analyte,
    //       category,
    //       group,
    //       location,
    //       sample_type,
    //     } = params;

    //     const comparisonDates =
    //       start_date && end_date
    //         ? calculateComparisonDateRange(start_date, end_date)
    //         : null;

    //     const getDateFilter = (field: string) => {
    //       if (start_date && end_date)
    //         return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
    //       if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
    //       if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
    //       return "";
    //     };

    //     const getWhereDate = () => {
    //       if (start_date && end_date)
    //         return `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`;
    //       if (start_date) return `AND (ds.date_collected >= '${start_date}')`;
    //       if (end_date) return `AND (ds.date_collected <= '${end_date}')`;
    //       return "";
    //     };

    //     const res = await queryDuck(`
    //     SELECT
    //       COUNT(*) as detected_unquantifiable_analytes,
    //       count(distinct "SAMPLE_NUMBER") ${
    //         start_date && end_date
    //           ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
    //           : ""
    //       } as period_samples,
    //       COUNT(DISTINCT "SAMPLE_NUMBER") ${
    //         comparisonDates?.comparison_start_date &&
    //         comparisonDates?.comparison_end_date
    //           ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
    //           : ""
    //       } as comparison_samples
    //     FROM
    //       summary_results_unified
    //       WHERE 1=1
    //         and "finalresult_value" =  'Detected'
    //         ${location ? `AND "CatchmentArea" = '${location}'` : ""}
    //         ${analyte ? `AND "name" = '${analyte}'` : ""}
    //         ${group ? `AND "analyte_group" = '${group}'` : ""}
    //         ${category ? `AND "labName" = '${category}'` : ""}
    //   `);

    //     const {
    //       period_samples: currentValue,
    //       comparison_samples: comparisonValue,
    //     } = res[0] || {};
    //     const percentageChange =
    //       comparisonValue > 0
    //         ? ((currentValue - comparisonValue) / comparisonValue) * 100
    //         : 0;

    //     return {
    //       id: 15,
    //       name: "Detected but Unquantifiable Analytes",
    //       type: "single_number",
    //       value: currentValue,
    //       change: {
    //         pct: capPercentageChange(percentageChange),
    //         direction:
    //           percentageChange > 0
    //             ? "up"
    //             : percentageChange < 0
    //             ? "down"
    //             : "center",
    //         prefix: "%",
    //       },
    //       legend: "<BLOQ",
    //     };
    //   },
    //   info: {
    //     id: 15,
    //     name: "Detected but Unquantifiable Analytes",
    //     description:
    //       "Analytes detected but below limit of quantification (detection_flag = 'Unknown')",
    //     category: "Sample Overview",
    //     parameters: [
    //       "level",
    //       "start_date",
    //       "end_date",
    //       "analyte",
    //       "category",
    //       "group",
    //       "location",
    //       "sample_type",
    //     ],
    //   },
    // },

    // "16": {
    //   get: async (params: any) => {
    //     const {
    //       start_date,
    //       end_date,
    //       analyte,
    //       category,
    //       group,
    //       location,
    //       sample_type,
    //     } = params;

    //     const comparisonDates =
    //       start_date && end_date
    //         ? calculateComparisonDateRange(start_date, end_date)
    //         : null;

    //     const res = await queryDuck(`
    //     SELECT
    //       count(distinct "name") ${
    //         start_date && end_date
    //           ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
    //           : ""
    //       } as period_samples,
    //       count(distinct "name") ${
    //         comparisonDates?.comparison_start_date &&
    //         comparisonDates?.comparison_end_date
    //           ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
    //           : ""
    //       } as comparison_samples
    //       FROM
    //       summary_results_unified
    //       WHERE 1=1
    //         and "finalresult_value" =  'Detected'
    //         and "ResultStatus" = 'A'
    //         ${
    //           location
    //             ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
    //             : ""
    //         }
    //         ${analyte ? `AND "name" = '${analyte}'` : ""}
    //         ${group ? `AND "analyte_group" = '${group}'` : ""}
    //         ${category ? `AND "labName" = '${category}'` : ""}
    //         ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
    //      `);

    //     const {
    //       period_samples: currentValue,
    //       comparison_samples: comparisonValue,
    //     } = res[0] || {};
    //     const percentageChange =
    //       comparisonValue > 0
    //         ? ((currentValue - comparisonValue) / comparisonValue) * 100
    //         : 0;

    //     return {
    //       id: 16,
    //       name: "Detected Analytes",
    //       type: "single_number",
    //       value: currentValue,
    //       change: {
    //         pct: capPercentageChange(percentageChange),
    //         direction:
    //           percentageChange > 0
    //             ? "up"
    //             : percentageChange < 0
    //             ? "down"
    //             : "center",
    //         prefix: "%",
    //       },
    //       legend: "Quantifiable",
    //     };
    //   },
    //   info: {
    //     id: 16,
    //     name: "Detected Analytes",
    //     description:
    //       "Analytes detected and quantifiable (detection_flag = 'Detected')",
    //     category: "Sample Overview",
    //     parameters: [
    //       "level",
    //       "start_date",
    //       "end_date",
    //       "analyte",
    //       "category",
    //       "group",
    //       "location",
    //       "sample_type",
    //     ],
    //   },
    // },

    // FIXME: asda
    // number of anomalies
    // qualitative -> ch -> d/not d
    // quantitative -> mb -> concentration

    // "17": {
    //   //  need clearificarion
    //   get: async (params: any) => {
    //     const {
    //       start_date,
    //       end_date,
    //       analyte,
    //       category,
    //       group,
    //       location,
    //       sample_type,
    //     } = params;

    //     const comparisonDates =
    //       start_date && end_date
    //         ? calculateComparisonDateRange(start_date, end_date)
    //         : null;

    //     const getDateFilter = (field: string) => {
    //       if (start_date && end_date)
    //         return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
    //       if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
    //       if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
    //       return "";
    //     };

    //     const getWhereDate = () => {
    //       if (start_date && end_date)
    //         return `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`;
    //       if (start_date) return `AND (ds.date_collected >= '${start_date}')`;
    //       if (end_date) return `AND (ds.date_collected <= '${end_date}')`;
    //       return "";
    //     };

    //     const res = await queryDuck(`
    //     SELECT
    //       COUNT(*) as qualitative_outliers,
    //       count(distinct "SAMPLE_NUMBER") ${
    //         start_date && end_date
    //           ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
    //           : ""
    //       } as period_samples,
    //       COUNT(DISTINCT "SAMPLE_NUMBER") ${
    //         comparisonDates?.comparison_start_date &&
    //         comparisonDates?.comparison_end_date
    //           ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
    //           : ""
    //       } as comparison_samples
    //     FROM
    //       summary_results_unified
    //       WHERE 1=1
    //         ${location ? `AND "CatchmentArea" = '${location}'` : ""}
    //         ${analyte ? `AND "name" = '${analyte}'` : ""}
    //         ${group ? `AND "analyte_group" = '${group}'` : ""}
    //         ${category ? `AND "labName" = '${category}'` : ""}
    //   `);

    //     const {
    //       period_samples: currentValue,
    //       comparison_samples: comparisonValue,
    //     } = res[0] || {};
    //     const percentageChange =
    //       comparisonValue > 0
    //         ? ((currentValue - comparisonValue) / comparisonValue) * 100
    //         : 0;

    //     return {
    //       id: 17,
    //       name: "Outliers in Qualitative Data",
    //       type: "single_number",
    //       value: currentValue,
    //       change: {
    //         pct: capPercentageChange(percentageChange),
    //         direction:
    //           percentageChange > 0
    //             ? "up"
    //             : percentageChange < 0
    //             ? "down"
    //             : "center",
    //         prefix: "%",
    //       },
    //       legend: "Flagged Sample",
    //     };
    //   },
    //   info: {
    //     id: 17,
    //     name: "Outliers in Qualitative Data",
    //     description: "Qualitative anomalies detected in test results",
    //     category: "Sample Overview",
    //     parameters: [
    //       "level",
    //       "start_date",
    //       "end_date",
    //       "analyte",
    //       "category",
    //       "group",
    //       "location",
    //     ],
    //   },
    // },
    // "18": {
    //  need clearificarion

    // get: async (params: any) => {
    //   const {
    //     start_date,
    //     end_date,
    //     analyte,
    //     category,
    //     group,
    //     location,
    //     sample_type,
    //   } = params;

    //   const comparisonDates =
    //     start_date && end_date
    //       ? calculateComparisonDateRange(start_date, end_date)
    //       : null;

    //   const getDateFilter = (field: string) => {
    //     if (start_date && end_date)
    //       return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
    //     if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
    //     if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
    //     return "";
    //   };

    //   const getWhereDate = () => {
    //     if (start_date && end_date)
    //       return `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`;
    //     if (start_date) return `AND (ds.date_collected >= '${start_date}')`;
    //     if (end_date) return `AND (ds.date_collected <= '${end_date}')`;
    //     return "";
    //   };

    //   const res = await query(`
    //     SELECT
    //       COUNT(*) as quantitative_outliers,
    //       COUNT(*) ${getDateFilter("ds.date_collected")} as period_samples,
    //       COUNT(*) ${
    //         comparisonDates?.comparison_start_date &&
    //         comparisonDates?.comparison_end_date
    //           ? `FILTER (WHERE ds.date_collected BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
    //           : ""
    //       } as comparison_samples
    //     FROM dwh.mart_anomalies ma
    //     JOIN dim_analyte da ON ma.analyte_key = da.analyte_key
    //     JOIN dwh.dim_location dl ON ma.location_key = dl.location_key

    //     WHERE ma.anomaly_type = 'quantitative'
    //     ${getWhereDate()}
    //     ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
    //     ${category ? `AND (da.lab_classification = '${category}')` : ""}
    //     ${group ? `AND (da.analyte_group = '${group}')` : ""}
    //     ${location ? `AND (dl.catchment_name = '${location}')` : ""}
    //   `);

    //   const {
    //     period_samples: currentValue,
    //     comparison_samples: comparisonValue,
    //   } = res[0] || {};
    //   const percentageChange =
    //     comparisonValue > 0
    //       ? ((currentValue - comparisonValue) / comparisonValue) * 100
    //       : 0;

    //   return {
    //     id: 18,
    //     name: "Outliers in Quantitative Data",
    //     type: "single_number",
    //     value: currentValue,
    //     change: {
    //       pct: capPercentageChange(percentageChange),
    //       direction:
    //         percentageChange > 0
    //           ? "up"
    //           : percentageChange < 0
    //           ? "down"
    //           : "center",
    //       prefix: "%",
    //     },
    //     legend: "Flagged Sample",
    //   };
    // },
    // info: {
    //   id: 18,
    //   name: "Outliers in Quantitative Data",
    //   description: "Quantitative anomalies detected in test results",
    //   category: "Sample Overview",
    //   parameters: [
    //     "level",
    //     "start_date",
    //     "end_date",
    //     "analyte",
    //     "category",
    //     "group",
    //     "location",
    //   ],
    // },
    // },
    // "19": {
    // get: async (params: any) => {
    //   const {
    //     start_date,
    //     end_date,
    //     analyte,
    //     category,
    //     group,
    //     location,
    //     sample_type,
    //   } = params;

    //   const comparisonDates =
    //     start_date && end_date
    //       ? calculateComparisonDateRange(start_date, end_date)
    //       : null;

    //   const getWhereDate = () => {
    //     if (start_date && end_date)
    //       return `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`;
    //     if (start_date) return `AND (ds.date_collected >= '${start_date}')`;
    //     if (end_date) return `AND (ds.date_collected <= '${end_date}')`;
    //     return "";
    //   };

    //   const res = await query(`
    //     SELECT
    //       da.analyte_name,
    //       COUNT(*) as deviation_count,
    //       AVG(f.result_value) as avg_value,
    //       STDDEV(f.result_value) as std_dev
    //       FROM dwh.dim_sample ds
    //       JOIN dwh.fact_test_results f ON ds.sample_key = f.sample_key
    //       JOIN dwh.dim_location dl ON f.location_key = dl.location_key
    //       JOIN dim_analyte da ON f.analyte_key = da.analyte_key
    //       INNER JOIN dwh.dim_test dt ON f.test_key = dt.test_key

    //     WHERE f.detection_flag = 'Detected'
    //     AND f.result_value IS NOT NULL
    //     ${getWhereDate()}
    //     ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
    //     ${category ? `AND (da.lab_classification = '${category}')` : ""}
    //     ${group ? `AND (da.analyte_group = '${group}')` : ""}
    //     ${sample_type ? `AND (dt.sample_type = '${sample_type}')` : ""}
    //     ${location ? `AND (dl.catchment_name = '${location}')` : ""}
    //     GROUP BY da.analyte_name
    //     HAVING COUNT(*) > 10
    //     ORDER BY deviation_count DESC
    //     LIMIT 5
    //   `);

    //   const totalDeviations = res.reduce(
    //     (sum, row) => sum + row.deviation_count,
    //     0
    //   );

    //   return {
    //     id: 19,
    //     name: "Analyte Tests Consistently High/Low",
    //     type: "multi_value",
    //     value: totalDeviations,
    //     change: {
    //       pct: 0,
    //       direction: "",
    //       prefix: "",
    //     },
    //     legend: "Deviation Monitoring",

    //   };
    // },
    // info: {
    //   id: 19,
    //   name: "Analyte Tests Consistently High/Low",
    //   description: "Analytes with consistent high/low concentration patterns",
    //   category: "Sample Overview",
    //   parameters: [
    //     "level",
    //     "start_date",
    //     "end_date",
    //     "analyte",
    //     "category",
    //     "group",
    //     "location",
    //     "sample_type",
    //   ],
    // },
    // },
    "20": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
        SELECT
          count(distinct "Test_Number") ${
            start_date && end_date
              ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
              : ""
          } as period_samples,
          COUNT(DISTINCT "Test_Number") ${
            comparisonDates?.comparison_start_date &&
            comparisonDates?.comparison_end_date
              ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
              : ""
          } as comparison_samples
          FROM
          summary_results_unified
          WHERE 1=1
            and "ResultStatus" =  'A'
            ${
              location
                ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
                : ""
            }
            ${analyte ? `AND "name" = '${analyte}'` : ""}
            ${group ? `AND "analyte_group" = '${group}'` : ""}
            ${category ? `AND "labName" = '${category}'` : ""}
            ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
         `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 20,
          name: "Test Count for Authorized samples",
          type: "single_number",
          value: currentValue,
          change: {
            pct: capPercentageChange(percentageChange),
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Total",
        };
      },
      info: {
        id: 20,
        name: "Test Count for Authorized samples",
        description: "Total number of tests performed for Authorized sample",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "21": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
          SELECT 
            count(distinct "SAMPLE_NUMBER") ${
              start_date && end_date
                ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
                : ""
            } as period_samples,
            COUNT(DISTINCT "SAMPLE_NUMBER") ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM
            summary_results_unified
            WHERE 1=1
              and "SampleStatus" =  'R'
              ${
                location
                  ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
                  : ""
              }
                       ${analyte ? `AND "name" = '${analyte}'` : ""}
         ${group ? `AND "analyte_group" = '${group}'` : ""}
         ${category ? `AND "labName" = '${category}'` : ""}
         ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 21,
          name: "Rejected Samples",
          type: "single_number",
          value: currentValue,
          change: {
            pct: capPercentageChange(percentageChange),
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "QC Failure",
        };
      },
      info: {
        id: 21,
        name: "Rejected Samples",
        description:
          "Samples rejected due to quality control failures (sample_status = 'R')",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "location",
          "sample_type",
        ],
      },
    },
    "22": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
          SELECT 
            count(distinct "SAMPLE_NUMBER") ${
              start_date && end_date
                ? `FILTER (WHERE "sampled_date" BETWEEN '${start_date}' AND '${end_date}') `
                : ""
            } as period_samples,
            COUNT(DISTINCT "SAMPLE_NUMBER") ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM
                    summary_results_unified
                WHERE 1=1
                and "SampleStatus" =  'X'
                ${
                  location
                    ? `AND ("CatchmentArea" = '${location}'  OR "GIS_ID" = '${location}')`
                    : ""
                }
                ${analyte ? `AND "name" = '${analyte}'` : ""}
                ${group ? `AND "analyte_group" = '${group}'` : ""}
                ${category ? `AND "labName" = '${category}'` : ""}
                ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
       
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 22,
          name: "Cancelled Samples",
          type: "single_number",
          value: currentValue,
          change: {
            pct: capPercentageChange(percentageChange),
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Withdrawn by Request",
        };
      },
      info: {
        id: 22,
        name: "Cancelled Samples",
        description:
          "Samples cancelled/withdrawn by request (sample_status = 'C')",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "location",
          "sample_type",
        ],
      },
    },
  },
  // NOTE: DONE - pending Radioactivity Risks --update summary_results_unified set labName = 'Radioactivity';
  risk_trends_cards: {
    "1": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
          persona,
          risks,
          historical,
        } = params;

        let personaList = persona
          ? persona
              .split(",")
              ?.filter(Boolean)
              ?.filter((p) => p.trim())
          : [];

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
           SELECT
              count(*) ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${start_date}' and '${end_date}' or  rs.last_risk_date between '${start_date}' and '${end_date}'  ) 
                `
                  : ""
              }
              as period_count,

              count(*) 
              ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}' or  rs.last_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}') 
                      `
                  : ""
              }
              as prev_period_count


              from risk_score_info_summaries rs
              LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
              WHERE 1=1
              and lastGroup = true
              and risk_count > 1 
              ${historical ? "" : " and first_risk_date >= '2025-05-01' "}
             
              
              

             ${location ? `AND rs.GIS_ID = '${location}'` : ""}       
            ${analyte ? `AND da.name = '${analyte}'` : ""}
            ${category ? `AND da.labName = '${category}'` : ""}
            ${group ? `AND da.Analyte_Group = '${group}'` : ""}
            ${
              personaList
                ? (() => {
                    const list = Array.isArray(personaList)
                      ? personaList
                      : String(personaList)
                          .split(",")
                          .map((s) => s.trim())
                          .filter(Boolean);
                    return list.length
                      ? `AND da.persona IN (${list
                          .map((p) => `'${p.replace(/'/g, "''")}'`)
                          .join(",")})`
                      : "";
                  })()
                : ""
            }
        `);

        let personaRes: any[] = [];
        personaRes = await queryDuck(`
        SELECT
        da.persona,
              count(*) ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${start_date}' and '${end_date}' or  rs.last_risk_date between '${start_date}' and '${end_date}'  ) 
                `
                  : ""
              }
              as period_count,

              count(*) 
              ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}' or  rs.last_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}') 
                      `
                  : ""
              }
              as prev_period_count


              from risk_score_info_summaries rs
              LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
              WHERE 1=1
              and lastGroup = true
              and risk_count > 1  ${
                historical ? "" : " and first_risk_date >= '2025-05-01' "
              } 
            ${location ? `AND rs.GIS_ID = '${location}'` : ""}
            ${analyte ? `AND da.name = '${analyte}'` : ""}
            ${category ? `AND da.labName = '${category}'` : ""}
            ${group ? `AND da.Analyte_Group = '${group}'` : ""}
            ${
              personaList
                ? (() => {
                    const list = Array.isArray(personaList)
                      ? personaList
                      : String(personaList)
                          .split(",")
                          .map((s) => s.trim())
                          .filter(Boolean);
                    return list.length
                      ? `AND da.persona IN (${list
                          .map((p) => `'${p.replace(/'/g, "''")}'`)
                          .join(",")})`
                      : "";
                  })()
                : ""
            }
            group by da.persona
         `);

        const {
          period_count: currentValue,
          prev_period_count: comparisonValue,
        } = res[0];

        let percentageChange = 0;
        let hasComparison = false;

        if (
          start_date &&
          end_date &&
          comparisonDates &&
          comparisonValue &&
          comparisonValue > 0
        ) {
          percentageChange =
            (((currentValue || 0) - comparisonValue) / comparisonValue) * 100;
          hasComparison = true;
        }

        const finalValue = currentValue || 0;
        const finalPct = hasComparison
          ? capPercentageChange(percentageChange)
          : 0;

        const finalDirection = hasComparison
          ? percentageChange > 0
            ? "up"
            : percentageChange < 0
            ? "down"
            : "center"
          : "center";

        const personaCards =
          personaRes.length > 0
            ? personaRes.map((personaData, index) => {
                const personaCurrentValue = personaData.period_count || 0;
                const personaComparisonValue =
                  personaData.prev_period_count || 0;

                let personaPercentageChange = 0;
                let personaHasComparison = false;

                if (
                  start_date &&
                  end_date &&
                  comparisonDates &&
                  personaComparisonValue &&
                  personaComparisonValue > 0
                ) {
                  personaPercentageChange =
                    (((personaCurrentValue || 0) - personaComparisonValue) /
                      personaComparisonValue) *
                    100;
                  personaHasComparison = true;
                }

                const personaFinalPct = personaHasComparison
                  ? capPercentageChange(personaPercentageChange)
                  : 0;
                const personaFinalDirection = personaHasComparison
                  ? personaPercentageChange > 0
                    ? "up"
                    : personaPercentageChange < 0
                    ? "down"
                    : "center"
                  : "center";

                return {
                  name: `${personaData.persona}`,
                  type: "two_numbers",
                  left_side: {
                    main: {
                      value: personaCurrentValue,
                      label: "risk",
                    },
                    legend: {
                      type: "change",
                      pct: personaFinalPct,
                      direction: personaFinalDirection,
                      prefix: "%",
                    },
                  },
                  right_side: {
                    main: {
                      value: 0,
                      label: "Forecast",
                    },
                    legend: {
                      type: "margin",
                      amount: 0,
                      prefix: "%",
                      label: "Uncertainty",
                    },
                  },
                };
              })
            : [];

        const allRisks = await queryDuck(`
            SELECT
                  risk_id,
                  analyte_name,
                  GIS_ID,
                  first_risk_date,
                  last_risk_date,
                  last_risk_score,
                  last_risk_category_id,
                  highest_risk_score,
                  highest_risk_score_category_id,
                  highest_risk_score_latest_date,
                  lowest_risk_score,
                  lowest_risk_score_category_id,
                  lowest_risk_score_latest_date,
                  risk_count,
                  da.persona
            from risk_score_info_summaries rs
            LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
            WHERE 1=1
            and lastGroup = true
            and risk_count > 1  ${
              historical ? "" : " and first_risk_date >= '2025-05-01' "
            } 
            ${
              start_date && end_date
                ? `
              and (rs.first_risk_date between '${start_date}' and '${end_date}' or  rs.last_risk_date between '${start_date}' and '${end_date}')
              `
                : ""
            }
            ${location ? `AND rs.GIS_ID = '${location}'` : ""}
            ${analyte ? `AND da.name = '${analyte}'` : ""}
            ${category ? `AND da.labName = '${category}'` : ""}
            ${group ? `AND da.Analyte_Group = '${group}'` : ""}
            ${
              personaList
                ? (() => {
                    const list = Array.isArray(personaList)
                      ? personaList
                      : String(personaList)
                          .split(",")
                          .map((s) => s.trim())
                          .filter(Boolean);
                    return list.length
                      ? `AND da.persona IN (${list
                          .map((p) => `'${p.replace(/'/g, "''")}'`)
                          .join(",")})`
                      : "";
                  })()
                : ""
            }
            
            `);

        return {
          id: 1,
          name: "Total Risks",
          type: "two_numbers",
          left_side: {
            main: {
              value: finalValue,
              label: "risk",
            },
            legend: {
              type: "change",
              pct: finalPct,
              value: comparisonValue,
              direction: finalDirection,
              prefix: "%",
            },
            list: allRisks,
          },
          right_side: null,
          persona_list: personaCards,
        };
      },
      info: {
        id: 1,
        name: "Total Risk",
      },
    },
    "2": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
          persona,
          risks,
          historical,
        } = params;

        let personaList = persona
          ? persona
              .split(",")
              ?.filter(Boolean)
              ?.filter((p) => p.trim())
          : [];

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
           SELECT
              count(*) ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${start_date}' and '${end_date}' or  rs.last_risk_date between '${start_date}' and '${end_date}'  ) 
                `
                  : ""
              }
              as period_count,

              count(*) 
              ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}' or  rs.last_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}') 
                      `
                  : ""
              }
              as prev_period_count


              from risk_score_info_summaries rs
              LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
              WHERE 1=1
              and lastGroup = true
              and risk_count > 1  ${
                historical ? "" : " and first_risk_date >= '2025-05-01' "
              } 
              and emerging_risk_flag = true
    
            ${location ? `AND rs.GIS_ID = '${location}'` : ""}
            ${analyte ? `AND da.name = '${analyte}'` : ""}
            ${category ? `AND da.labName = '${category}'` : ""}
            ${group ? `AND da.Analyte_Group = '${group}'` : ""}
            ${
              personaList
                ? (() => {
                    const list = Array.isArray(personaList)
                      ? personaList
                      : String(personaList)
                          .split(",")
                          .map((s) => s.trim())
                          .filter(Boolean);
                    return list.length
                      ? `AND da.persona IN (${list
                          .map((p) => `'${p.replace(/'/g, "''")}'`)
                          .join(",")})`
                      : "";
                  })()
                : ""
            }
        `);

        let personaRes: any[] = [];
        personaRes = await queryDuck(`
        SELECT
        da.persona,
              count(*) ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${start_date}' and '${end_date}' or  rs.last_risk_date between '${start_date}' and '${end_date}'  ) 
                `
                  : ""
              }
              as period_count,

              count(*) 
              ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}' or  rs.last_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}') 
                      `
                  : ""
              }
              as prev_period_count


              from risk_score_info_summaries rs
              LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
              WHERE 1=1
              and lastGroup = true
              and risk_count > 1  ${
                historical ? "" : " and first_risk_date >= '2025-05-01' "
              } 
              and emerging_risk_flag = true

            ${location ? `AND rs.GIS_ID = '${location}'` : ""}
            ${analyte ? `AND da.name = '${analyte}'` : ""}
            ${category ? `AND da.labName = '${category}'` : ""}
            ${group ? `AND da.Analyte_Group = '${group}'` : ""}
            ${
              personaList
                ? (() => {
                    const list = Array.isArray(personaList)
                      ? personaList
                      : String(personaList)
                          .split(",")
                          .map((s) => s.trim())
                          .filter(Boolean);
                    return list.length
                      ? `AND da.persona IN (${list
                          .map((p) => `'${p.replace(/'/g, "''")}'`)
                          .join(",")})`
                      : "";
                  })()
                : ""
            }
            group by da.persona
         `);

        const {
          period_count: currentValue,
          prev_period_count: comparisonValue,
        } = res[0];

        let percentageChange = 0;
        let hasComparison = false;

        if (
          start_date &&
          end_date &&
          comparisonDates &&
          comparisonValue &&
          comparisonValue > 0
        ) {
          percentageChange =
            (((currentValue || 0) - comparisonValue) / comparisonValue) * 100;
          hasComparison = true;
        }

        const finalValue = currentValue || 0;
        const finalPct = hasComparison
          ? capPercentageChange(percentageChange)
          : 0;

        const finalDirection = hasComparison
          ? percentageChange > 0
            ? "up"
            : percentageChange < 0
            ? "down"
            : "center"
          : "center";

        const personaCards =
          personaRes.length > 0
            ? personaRes.map((personaData, index) => {
                const personaCurrentValue = personaData.period_count || 0;
                const personaComparisonValue =
                  personaData.prev_period_count || 0;

                let personaPercentageChange = 0;
                let personaHasComparison = false;

                if (
                  start_date &&
                  end_date &&
                  comparisonDates &&
                  personaComparisonValue &&
                  personaComparisonValue > 0
                ) {
                  personaPercentageChange =
                    (((personaCurrentValue || 0) - personaComparisonValue) /
                      personaComparisonValue) *
                    100;
                  personaHasComparison = true;
                }

                const personaFinalPct = personaHasComparison
                  ? capPercentageChange(personaPercentageChange)
                  : 0;
                const personaFinalDirection = personaHasComparison
                  ? personaPercentageChange > 0
                    ? "up"
                    : personaPercentageChange < 0
                    ? "down"
                    : "center"
                  : "center";

                return {
                  name: `${personaData.persona}`,
                  type: "two_numbers",
                  left_side: {
                    main: {
                      value: personaCurrentValue,
                      label: "risk",
                    },
                    legend: {
                      type: "change",
                      pct: personaFinalPct,
                      direction: personaFinalDirection,
                      prefix: "%",
                    },
                  },
                  right_side: {
                    main: {
                      value: 0,
                      label: "Forecast",
                    },
                    legend: {
                      type: "margin",
                      amount: 0,
                      prefix: "%",
                      label: "Uncertainty",
                    },
                  },
                };
              })
            : [];

        const allRisks = await queryDuck(`
            SELECT
                  risk_id,
                  analyte_name,
                  GIS_ID,
                  first_risk_date,
                  last_risk_date,
                  last_risk_score,
                  emerging_risk_flag,
                  last_risk_category_id,
                  highest_risk_score,
                  highest_risk_score_category_id,
                  highest_risk_score_latest_date,
                  lowest_risk_score,
                  lowest_risk_score_category_id,
                  lowest_risk_score_latest_date,
                  risk_count,
                  da.persona
            from risk_score_info_summaries rs
            LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
            WHERE 1=1
            and lastGroup = true
            and risk_count > 1  ${
              historical ? "" : " and first_risk_date >= '2025-05-01' "
            } 
            and emerging_risk_flag = true 
            ${
              start_date && end_date
                ? `
             and (rs.first_risk_date between '${start_date}' and '${end_date}' or  rs.last_risk_date between '${start_date}' and '${end_date}')
              `
                : ""
            }
            ${location ? `AND rs.GIS_ID = '${location}'` : ""}
            ${analyte ? `AND da.name = '${analyte}'` : ""}
            ${category ? `AND da.labName = '${category}'` : ""}
            ${group ? `AND da.Analyte_Group = '${group}'` : ""}
            ${
              personaList
                ? (() => {
                    const list = Array.isArray(personaList)
                      ? personaList
                      : String(personaList)
                          .split(",")
                          .map((s) => s.trim())
                          .filter(Boolean);
                    return list.length
                      ? `AND da.persona IN (${list
                          .map((p) => `'${p.replace(/'/g, "''")}'`)
                          .join(",")})`
                      : "";
                  })()
                : ""
            }
            
            `);

        return {
          id: 2,
          name: "Emerging Risks",
          type: "two_numbers",
          left_side: {
            main: {
              value: finalValue,
              label: "Emerging Risks",
            },
            legend: {
              type: "change",
              pct: finalPct,
              direction: finalDirection,
              prefix: "%",
            },
            list: allRisks,
          },
          right_side: null,
          persona_list: personaCards,
        };
      },
      info: {
        id: 2,
        name: "Emerging Threats",
      },
    },
    "3": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
          persona,
          risks,
          historical,
        } = params;

        let personaList = persona
          ? persona
              .split(",")
              ?.filter(Boolean)
              ?.filter((p) => p.trim())
          : [];

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
           SELECT
              count(*) ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${start_date}' and '${end_date}' or  rs.last_risk_date between '${start_date}' and '${end_date}'  ) 
                `
                  : ""
              }
              as period_count,

              count(*) 
              ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}' or  rs.last_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}') 
                      `
                  : ""
              }
              as prev_period_count


              from risk_score_info_summaries rs
              LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
              WHERE 1=1
              and lastGroup = true
              and risk_count > 1  ${
                historical ? "" : " and first_risk_date >= '2025-05-01' "
              } 
              and da.labName = 'Radioactivity'    
            ${location ? `AND rs.GIS_ID = '${location}'` : ""}
            ${analyte ? `AND da.name = '${analyte}'` : ""}
            ${category ? `AND da.labName = '${category}'` : ""}
            ${group ? `AND da.Analyte_Group = '${group}'` : ""}
            ${
              personaList
                ? (() => {
                    const list = Array.isArray(personaList)
                      ? personaList
                      : String(personaList)
                          .split(",")
                          .map((s) => s.trim())
                          .filter(Boolean);
                    return list.length
                      ? `AND da.persona IN (${list
                          .map((p) => `'${p.replace(/'/g, "''")}'`)
                          .join(",")})`
                      : "";
                  })()
                : ""
            }
        `);

        let personaRes: any[] = [];
        personaRes = await queryDuck(`
        SELECT
        da.persona,
              count(*) ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${start_date}' and '${end_date}' or  rs.last_risk_date between '${start_date}' and '${end_date}'  ) 
                `
                  : ""
              }
              as period_count,

              count(*) 
              ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}' or  rs.last_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}') 
                      `
                  : ""
              }
              as prev_period_count


              from risk_score_info_summaries rs
              LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
              WHERE 1=1
              and lastGroup = true
              and risk_count > 1  ${
                historical ? "" : " and first_risk_date >= '2025-05-01' "
              } 
              and da.labName = 'Radioactivity'

            ${location ? `AND rs.GIS_ID = '${location}'` : ""}
            ${analyte ? `AND da.name = '${analyte}'` : ""}
            ${category ? `AND da.labName = '${category}'` : ""}
            ${group ? `AND da.Analyte_Group = '${group}'` : ""}
            ${
              personaList
                ? (() => {
                    const list = Array.isArray(personaList)
                      ? personaList
                      : String(personaList)
                          .split(",")
                          .map((s) => s.trim())
                          .filter(Boolean);
                    return list.length
                      ? `AND da.persona IN (${list
                          .map((p) => `'${p.replace(/'/g, "''")}'`)
                          .join(",")})`
                      : "";
                  })()
                : ""
            }
            group by da.persona
         `);

        const {
          period_count: currentValue,
          prev_period_count: comparisonValue,
        } = res[0];

        let percentageChange = 0;
        let hasComparison = false;

        if (
          start_date &&
          end_date &&
          comparisonDates &&
          comparisonValue &&
          comparisonValue > 0
        ) {
          percentageChange =
            (((currentValue || 0) - comparisonValue) / comparisonValue) * 100;
          hasComparison = true;
        }

        const finalValue = currentValue || 0;
        const finalPct = hasComparison
          ? capPercentageChange(percentageChange)
          : 0;

        const finalDirection = hasComparison
          ? percentageChange > 0
            ? "up"
            : percentageChange < 0
            ? "down"
            : "center"
          : "center";

        const personaCards =
          personaRes.length > 0
            ? personaRes.map((personaData, index) => {
                const personaCurrentValue = personaData.period_count || 0;
                const personaComparisonValue =
                  personaData.prev_period_count || 0;

                let personaPercentageChange = 0;
                let personaHasComparison = false;

                if (
                  start_date &&
                  end_date &&
                  comparisonDates &&
                  personaComparisonValue &&
                  personaComparisonValue > 0
                ) {
                  personaPercentageChange =
                    (((personaCurrentValue || 0) - personaComparisonValue) /
                      personaComparisonValue) *
                    100;
                  personaHasComparison = true;
                }

                const personaFinalPct = personaHasComparison
                  ? capPercentageChange(personaPercentageChange)
                  : 0;
                const personaFinalDirection = personaHasComparison
                  ? personaPercentageChange > 0
                    ? "up"
                    : personaPercentageChange < 0
                    ? "down"
                    : "center"
                  : "center";

                return {
                  name: `${personaData.persona}`,
                  type: "two_numbers",
                  left_side: {
                    main: {
                      value: personaCurrentValue,
                      label: "risk",
                    },
                    legend: {
                      type: "change",
                      pct: personaFinalPct,
                      direction: personaFinalDirection,
                      prefix: "%",
                    },
                  },
                  right_side: {
                    main: {
                      value: 0,
                      label: "Forecast",
                    },
                    legend: {
                      type: "margin",
                      amount: 0,
                      prefix: "%",
                      label: "Uncertainty",
                    },
                  },
                };
              })
            : [];

        const allRisks = await queryDuck(`
            SELECT
                  risk_id,
                  analyte_name,
                  GIS_ID,
                  first_risk_date,
                  last_risk_date,
                  last_risk_score,
                  emerging_risk_flag,
                  last_risk_category_id,
                  highest_risk_score,
                  highest_risk_score_category_id,
                  highest_risk_score_latest_date,
                  lowest_risk_score,
                  lowest_risk_score_category_id,
                  lowest_risk_score_latest_date,
                  risk_count,
                  da.persona
            from risk_score_info_summaries rs
            LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
            WHERE 1=1
            and lastGroup = true
            and risk_count > 1  ${
              historical ? "" : " and first_risk_date >= '2025-05-01' "
            } 
            and da.labName = 'Radioactivity' 
            ${
              start_date && end_date
                ? `
             and (rs.first_risk_date between '${start_date}' and '${end_date}' or  rs.last_risk_date between '${start_date}' and '${end_date}')
              `
                : ""
            }
            ${location ? `AND rs.GIS_ID = '${location}'` : ""}
            ${analyte ? `AND da.name = '${analyte}'` : ""}
            ${category ? `AND da.labName = '${category}'` : ""}
            ${group ? `AND da.Analyte_Group = '${group}'` : ""}
            ${
              personaList
                ? (() => {
                    const list = Array.isArray(personaList)
                      ? personaList
                      : String(personaList)
                          .split(",")
                          .map((s) => s.trim())
                          .filter(Boolean);
                    return list.length
                      ? `AND da.persona IN (${list
                          .map((p) => `'${p.replace(/'/g, "''")}'`)
                          .join(",")})`
                      : "";
                  })()
                : ""
            }
            
            `);

        return {
          id: 3,
          name: "Radioactivity Risks",
          type: "two_numbers",
          left_side: {
            main: {
              value: finalValue,
              label: "Radioactive",
            },
            legend: {
              type: "change",
              pct: finalPct,
              direction: finalDirection,
              prefix: "%",
            },
            list: allRisks,
          },
          right_side: {
            main: {
              value: 0,
              label: "Forecast",
            },
            legend: {
              type: "margin",
              amount: 0,
              prefix: "%",
              label: "Uncertainty",
            },
          },
          persona_list: personaCards,
        };
      },
      info: {
        id: 3,
        name: "Radioactivity Risks",
      },
    },
    "4": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
          persona,
          risks,
          historical,
        } = params;

        let personaList = persona
          ? persona
              .split(",")
              ?.filter(Boolean)
              ?.filter((p) => p.trim())
          : [];

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
           SELECT
              count(distinct GIS_ID) ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${start_date}' and '${end_date}' or  rs.last_risk_date between '${start_date}' and '${end_date}'  ) 
                `
                  : ""
              }
              as period_count,

              count(distinct GIS_ID) 
              ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}' or  rs.last_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}') 
                      `
                  : ""
              }
              as prev_period_count


              from risk_score_info_summaries rs
              LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
              WHERE 1=1
              and lastGroup = true
              and risk_count > 1  ${
                historical ? "" : " and first_risk_date >= '2025-05-01' "
              } 
              and rs.last_risk_category_id = '5'    
            ${location ? `AND rs.GIS_ID = '${location}'` : ""}
            ${analyte ? `AND da.name = '${analyte}'` : ""}
            ${category ? `AND da.labName = '${category}'` : ""}
            ${group ? `AND da.Analyte_Group = '${group}'` : ""}
            ${
              personaList
                ? (() => {
                    const list = Array.isArray(personaList)
                      ? personaList
                      : String(personaList)
                          .split(",")
                          .map((s) => s.trim())
                          .filter(Boolean);
                    return list.length
                      ? `AND da.persona IN (${list
                          .map((p) => `'${p.replace(/'/g, "''")}'`)
                          .join(",")})`
                      : "";
                  })()
                : ""
            }
        `);

        let personaRes: any[] = [];
        personaRes = await queryDuck(`
        SELECT
        da.persona,
              count(distinct GIS_ID) ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${start_date}' and '${end_date}' or  rs.last_risk_date between '${start_date}' and '${end_date}'  ) 
                `
                  : ""
              }
              as period_count,

              count(distinct GIS_ID) 
              ${
                start_date && end_date
                  ? `
                filter (where rs.first_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}' or  rs.last_risk_date between '${comparisonDates.comparison_start_date}' and '${comparisonDates.comparison_end_date}') 
                      `
                  : ""
              }
              as prev_period_count


              from risk_score_info_summaries rs
              LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
              WHERE 1=1
              and lastGroup = true
              and risk_count > 1  ${
                historical ? "" : " and first_risk_date >= '2025-05-01' "
              } 
              and rs.last_risk_category_id = '5'

            ${location ? `AND rs.GIS_ID = '${location}'` : ""}
            ${analyte ? `AND da.name = '${analyte}'` : ""}
            ${category ? `AND da.labName = '${category}'` : ""}
            ${group ? `AND da.Analyte_Group = '${group}'` : ""}
            ${
              personaList
                ? (() => {
                    const list = Array.isArray(personaList)
                      ? personaList
                      : String(personaList)
                          .split(",")
                          .map((s) => s.trim())
                          .filter(Boolean);
                    return list.length
                      ? `AND da.persona IN (${list
                          .map((p) => `'${p.replace(/'/g, "''")}'`)
                          .join(",")})`
                      : "";
                  })()
                : ""
            }
            group by da.persona
         `);

        const {
          period_count: currentValue,
          prev_period_count: comparisonValue,
        } = res[0];

        let percentageChange = 0;
        let hasComparison = false;

        if (
          start_date &&
          end_date &&
          comparisonDates &&
          comparisonValue &&
          comparisonValue > 0
        ) {
          percentageChange =
            (((currentValue || 0) - comparisonValue) / comparisonValue) * 100;
          hasComparison = true;
        }

        const finalValue = currentValue || 0;
        const finalPct = hasComparison
          ? capPercentageChange(percentageChange)
          : 0;

        const finalDirection = hasComparison
          ? percentageChange > 0
            ? "up"
            : percentageChange < 0
            ? "down"
            : "center"
          : "center";

        const personaCards =
          personaRes.length > 0
            ? personaRes.map((personaData, index) => {
                const personaCurrentValue = personaData.period_count || 0;
                const personaComparisonValue =
                  personaData.prev_period_count || 0;

                let personaPercentageChange = 0;
                let personaHasComparison = false;

                if (
                  start_date &&
                  end_date &&
                  comparisonDates &&
                  personaComparisonValue &&
                  personaComparisonValue > 0
                ) {
                  personaPercentageChange =
                    (((personaCurrentValue || 0) - personaComparisonValue) /
                      personaComparisonValue) *
                    100;
                  personaHasComparison = true;
                }

                const personaFinalPct = personaHasComparison
                  ? capPercentageChange(personaPercentageChange)
                  : 0;
                const personaFinalDirection = personaHasComparison
                  ? personaPercentageChange > 0
                    ? "up"
                    : personaPercentageChange < 0
                    ? "down"
                    : "center"
                  : "center";

                return {
                  name: `${personaData.persona}`,
                  type: "two_numbers",
                  left_side: {
                    main: {
                      value: personaCurrentValue,
                      label: "risk",
                    },
                    legend: {
                      type: "change",
                      pct: personaFinalPct,
                      direction: personaFinalDirection,
                      prefix: "%",
                    },
                  },
                  right_side: {
                    main: {
                      value: 0,
                      label: "Forecast",
                    },
                    legend: {
                      type: "margin",
                      amount: 0,
                      prefix: "%",
                      label: "Uncertainty",
                    },
                  },
                };
              })
            : [];

        const allRisks = await queryDuck(`
            SELECT
              GIS_ID,
              count(*) as risks_count
            from risk_score_info_summaries rs
            LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
            WHERE 1=1
            and lastGroup = true
            and risk_count > 1  ${
              historical ? "" : " and first_risk_date >= '2025-05-01' "
            } 
            and rs.last_risk_category_id = '5'
            ${
              start_date && end_date
                ? `
             and (rs.first_risk_date between '${start_date}' and '${end_date}' or  rs.last_risk_date between '${start_date}' and '${end_date}')
              `
                : ""
            }
            ${location ? `AND rs.GIS_ID = '${location}'` : ""}
            ${analyte ? `AND da.name = '${analyte}'` : ""}
            ${category ? `AND da.labName = '${category}'` : ""}
            ${group ? `AND da.Analyte_Group = '${group}'` : ""}
            ${
              personaList
                ? (() => {
                    const list = Array.isArray(personaList)
                      ? personaList
                      : String(personaList)
                          .split(",")
                          .map((s) => s.trim())
                          .filter(Boolean);
                    return list.length
                      ? `AND da.persona IN (${list
                          .map((p) => `'${p.replace(/'/g, "''")}'`)
                          .join(",")})`
                      : "";
                  })()
                : ""
            }
            group by GIS_ID
            
            `);

        return {
          id: 4,
          name: "Critical Regions",
          type: "two_numbers",
          left_side: {
            main: {
              value: finalValue,
              label: "Regions",
            },
            legend: {
              type: "change",
              pct: finalPct,
              direction: finalDirection,
              prefix: "%",
            },
            list: allRisks,
          },
          right_side: null,
          persona_list: personaCards,
        };
      },
      info: {
        id: 4,
        name: "Critical Regions",
      },
    },
  },
  analyte_insights_cards: {
    "1": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
          lab,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
         WITH base AS (
  SELECT
    s.*,
    l.loq_value
  FROM summary_results_unified s
  LEFT JOIN analyte_loq_clean l
    ON s.name = l.Analyte
  WHERE "ResultStatus" = 'A'
    ${
      location
        ? `AND ( "CatchmentArea" = '${location}' OR "GIS_ID" = '${location}' )`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
    ${lab ? `AND "Lab" = '${lab}'` : ""}
),
dedup AS (
  SELECT
    b.*,
    ROW_NUMBER() OVER (
      PARTITION BY b."SAMPLE_NUMBER", b."name"
      ORDER BY b."DATE_COMPLETED" DESC
    ) AS rn
  FROM base b
),
latest AS (
  SELECT * FROM dedup WHERE rn = 1
),
tagged AS (
  SELECT
    l.*,
    CASE
      -- Special case
      WHEN l."ANALYSIS" = 'RADIOACTIVITY' THEN
        (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))

      -- Numeric rawResult + LOQ available
      WHEN TRY_CAST(l."rawResult" AS DOUBLE) IS NOT NULL AND l.loq_value IS NOT NULL THEN
        CASE
          WHEN CAST(l."rawResult" AS DOUBLE) > l.loq_value THEN TRUE
          ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
        END

      -- Fallback to FinalResult_Value
      ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
    END AS is_detected
  FROM latest l
),
classified AS (
  SELECT
    "SAMPLE_NUMBER",
    "sampled_date",
    "CatchmentArea",
    "GIS_ID",
    "name",
    "analyte_group",
    "labName",
    "sample_type",
    "Lab",
    CASE WHEN is_detected THEN 'Detected' ELSE 'Not Detected' END AS finalresult_value
  FROM tagged
)
SELECT
  "name" AS analyte,

  COUNT(DISTINCT "SAMPLE_NUMBER") FILTER (
    WHERE finalresult_value = 'Detected'
      ${
        start_date && end_date
          ? `AND "sampled_date" BETWEEN '${start_date}' AND '${end_date}'`
          : ""
      }
  ) AS period_present_analytes,

  COUNT(DISTINCT "SAMPLE_NUMBER") FILTER (
    WHERE finalresult_value = 'Detected'
      ${
        start_date && end_date
          ? `AND "sampled_date" BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}'`
          : ""
      }
  ) AS comparison_present_analytes,

  COUNT(DISTINCT "SAMPLE_NUMBER") FILTER (
    WHERE finalresult_value = 'Not Detected'
      ${
        start_date && end_date
          ? `AND "sampled_date" BETWEEN '${start_date}' AND '${end_date}'`
          : ""
      }
  ) AS period_absent_analytes,

  COUNT(DISTINCT "SAMPLE_NUMBER") FILTER (
    WHERE finalresult_value = 'Not Detected'
      ${
        start_date && end_date
          ? `AND "sampled_date" BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}'`
          : ""
      }
  ) AS comparison_absent_analytes

FROM classified
GROUP BY "name"
ORDER BY "name";


           
          `);

       

        return {
          id: 1,
          name: "Sample Presence",
          type: "two_numbers",
          left_side: {
            main: {
              value: res[0]?.period_present_analytes || 0,
              label: "Present",
            },
            legend: null, // removed the change based on the client request
          },
          right_side: {
            main: {
              value: res[0]?.period_absent_analytes || 0,
              label: "Absent",
            },
            legend: null,
          },
          list: null,
        };
      },
      info: {
        id: 1,
        name: "Analyte Presence",
        description:
          "Present vs Absent analytes count and comparison (detection_flag)",
        category: "Analyte Insights",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
        ],
      },
    },
    "2": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          lab,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const res = await queryDuck(`
          WITH base AS (
  SELECT
    s.*,
    l.loq_value
  FROM summary_results_unified s
  LEFT JOIN analyte_loq_clean l
    ON s.name = l.Analyte
  WHERE "ResultStatus" = 'A'
    ${
      location
        ? `AND ( "CatchmentArea" = '${location}' OR "GIS_ID" = '${location}' )`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
    ${lab ? `AND "Lab" = '${lab}'` : ""}
),
dedup AS (
  -- Keep ONE test per analyte per sample: latest by DATE_COMPLETED
  SELECT
    b.*,
    ROW_NUMBER() OVER (
      PARTITION BY b."SAMPLE_NUMBER", b."name"
      ORDER BY b."DATE_COMPLETED" DESC
    ) AS rn
  FROM base b
),
latest AS (
  SELECT * FROM dedup WHERE rn = 1
),
tagged AS (
  -- Compute detection on the kept row
  SELECT
    l.*,
    CASE
      WHEN l."ANALYSIS" = 'RADIOACTIVITY' THEN
        (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
      WHEN TRY_CAST(l."rawResult" AS DOUBLE) IS NOT NULL AND l.loq_value IS NOT NULL THEN
        CASE
          WHEN CAST(l."rawResult" AS DOUBLE) > l.loq_value THEN TRUE
          ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
        END
      ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
    END AS is_detected
  FROM latest l
),
classified AS (
  SELECT
    "SAMPLE_NUMBER",
    sampled_date,
    "CatchmentArea",
    "GIS_ID",
    "name",
    "analyte_group",
    "labName",
    "sample_type",
    "Lab",
    CASE WHEN is_detected THEN 'Detected' ELSE 'Not Detected' END AS finalresult_value
  FROM tagged
)
SELECT 
  COUNT(*) FILTER (
    WHERE 1=1
      ${
        start_date && end_date
          ? `AND sampled_date BETWEEN '${start_date}' AND '${end_date}'`
          : ""
      }
  ) AS period_total_analytes,

  COUNT(*) FILTER (
    WHERE finalresult_value = 'Detected'
      ${
        start_date && end_date
          ? `AND sampled_date BETWEEN '${start_date}' AND '${end_date}'`
          : ""
      }
  ) AS period_present_analytes,

  COUNT(*) FILTER (
    WHERE finalresult_value = 'Not Detected'
      ${
        start_date && end_date
          ? `AND sampled_date BETWEEN '${start_date}' AND '${end_date}'`
          : ""
      }
  ) AS period_absent_analytes,

  COUNT(*) FILTER (
    WHERE 1=1
      ${
        start_date && end_date
          ? `AND sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}'`
          : ""
      }
  ) AS comparison_total_analytes,

  COUNT(*) FILTER (
    WHERE finalresult_value = 'Detected'
      ${
        start_date && end_date
          ? `AND sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}'`
          : ""
      }
  ) AS comparison_present_analytes,

  COUNT(*) FILTER (
    WHERE finalresult_value = 'Not Detected'
      ${
        start_date && end_date
          ? `AND sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}'`
          : ""
      }
  ) AS comparison_absent_analytes
FROM classified;

        `);

        const totalPeriod = res[0].period_total_analytes || 0;
        const totalComparison = res[0].comparison_total_analytes || 0;

        const periodPresentPct = totalPeriod
          ? (res[0].period_present_analytes / totalPeriod) * 100
          : 0;
        const periodAbsentPct = totalPeriod
          ? (res[0].period_absent_analytes / totalPeriod) * 100
          : 0;

        const comparisonPresentPct = totalComparison
          ? (res[0].comparison_present_analytes / totalComparison) * 100
          : 0;
        const comparisonAbsentPct = totalComparison
          ? (res[0].comparison_absent_analytes / totalComparison) * 100
          : 0;

        const detectedInfo = getChangeInfo(
          periodPresentPct,
          comparisonPresentPct
        );
        const notDetectedInfo = getChangeInfo(
          periodAbsentPct,
          comparisonAbsentPct
        );

        function getChangeInfo(current: number, previous: number) {
          if (+previous === 0) {
            return {
              change: +current > 0 ? 100 : 0,
              direction: +current > 0 ? "up" : "center",
            };
          }

          const change: number = ((+current - +previous) / +previous) * 100;
          return {
            change: Math.abs(+change.toFixed(2)),
            direction: change > 0 ? "up" : change < 0 ? "down" : "center",
          };
        }

        return {
          id: 2,
          name: "Presence Rate",
          type: "two_numbers",
          left_side: {
            main: { value: +periodPresentPct.toFixed(2), label: "% Detected" },
            legend: {
              type: "change",
              pct: detectedInfo.change,
              direction: detectedInfo.direction,
              prefix: "%",
            },
          },
          right_side: {
            main: {
              value: +periodAbsentPct.toFixed(2),
              label: "% Not Detected",
            },
            legend: {
              type: "change",
              pct: notDetectedInfo.change,
              direction: notDetectedInfo.direction,
              prefix: "%",
            },
          },
          list: null,
        };
      },
      info: {
        id: 2,
        name: "Presence Rate",
        description: "",
        category: "Analyte Insights",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
        ],
      },
    },
    "3": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
          lab,
        } = params;
        console.log(lab, analyte);

        if (lab === "CHEM") {
          if (!analyte) {
            return {
              disabled: true,
              id: 3,
              name: "Concentration Rate",
              type: "two_numbers",
              left_side: {
                main: { value: 0, label: "-" },
                legend: { type: "change", pct: 0, direction: "-", prefix: "%" },
              },
              right_side: { main: { value: 0, label: "-" } },
              list: null,
            };
          }

          const comparisonDates =
            start_date && end_date
              ? calculateComparisonDateRange(start_date, end_date)
              : null;

          const res = await queryDuck(`
    WITH chem AS (
      SELECT
        SAMPLE_NUMBER,
        sampled_date,
        TRY_CAST(rawResult AS DOUBLE) AS raw_num
      FROM summary_results_unified
      WHERE "ResultStatus" = 'A'
        AND "Lab" = 'CHEM'
        AND "ANALYSIS" <> 'RADIOACTIVITY'
        AND "name" = '${analyte}'
        ${category ? `AND "labName" = '${category}'` : ""}
        ${group ? `AND "analyte_group" = '${group}'` : ""}
        ${
          location
            ? `AND ( "CatchmentArea" = '${location}' OR "GIS_ID" = '${location}' )`
            : ""
        }
        ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
        AND TRY_CAST(rawResult AS DOUBLE) IS NOT NULL
        and TRY_CAST(rawResult AS DOUBLE) > 0
    )
    SELECT
      -- period window
      MIN(raw_num) FILTER (
        WHERE 1=1
          ${
            start_date && end_date
              ? `AND sampled_date BETWEEN '${start_date}' AND '${end_date}'`
              : ""
          }
      ) AS period_min_raw,
      MAX(raw_num) FILTER (
        WHERE 1=1
          ${
            start_date && end_date
              ? `AND sampled_date BETWEEN '${start_date}' AND '${end_date}'`
              : ""
          }
      ) AS period_max_raw,

      -- comparison window
      MIN(raw_num) FILTER (
        WHERE 1=1
          ${
            start_date && end_date
              ? `AND sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}'`
              : ""
          }
      ) AS comparison_min_raw,
      MAX(raw_num) FILTER (
        WHERE 1=1
          ${
            start_date && end_date
              ? `AND sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}'`
              : ""
          }
      ) AS comparison_max_raw
    FROM chem;
  `);
          console.log(res);

          const periodMin = res?.[0]?.period_min_raw ?? 0;
          const periodMax = res?.[0]?.period_max_raw ?? 0;
          const compMin = res?.[0]?.comparison_min_raw ?? 0;
          const compMax = res?.[0]?.comparison_max_raw ?? 0;

          function getChangeInfo(current: number, previous: number) {
            if (+previous === 0) {
              return {
                change: +current > 0 ? 100 : 0,
                direction: +current > 0 ? "up" : "center",
              };
            }
            const change: number = ((+current - +previous) / +previous) * 100;
            return {
              change: Math.abs(+change.toFixed(2)),
              direction: change > 0 ? "up" : change < 0 ? "down" : "center",
            };
          }

          const minInfo = getChangeInfo(periodMin, compMin);
          const maxInfo = getChangeInfo(periodMax, compMax);

          return {
            id: 3,
            name: "Concentration Rate",
            type: "two_numbers",
            left_side: {
              main: { value: +(+periodMin), label: "Min" },
              // comparison vs previous window
              legend:
                maxInfo.change || minInfo.change
                  ? {
                      type: "change",
                      pct: minInfo.change,
                      direction: minInfo.direction,
                      prefix: "%",
                    }
                  : null,
            },
            right_side: {
              main: { value: +(+periodMax), label: "Max" },
              legend:
                maxInfo.change || minInfo.change
                  ? {
                      type: "change",
                      pct: maxInfo.change,
                      direction: maxInfo.direction,
                      prefix: "%",
                    }
                  : null,
            },
            list: null,
          };
        } else if (lab === "MICRO") {
          const comparisonDates =
            start_date && end_date
              ? calculateComparisonDateRange(start_date, end_date)
              : null;

          const res = await queryDuck(`
    WITH base AS (
      SELECT
        name,
        sampled_date
      FROM summary_results_unified
      WHERE "ResultStatus" = 'A'
        AND (
          "Lab" = 'MICRO'
          OR ("Lab" = 'CHEM' AND "ANALYSIS" = 'RADIOACTIVITY')  -- include CHEM radioactivity
        )
        AND UPPER("FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED') -- detection via FinalResult_Value only
        ${
          analyte ? `AND "name" = '${analyte}'` : ""
        }                      -- optional: allow drill-down by analyte if provided
        ${category ? `AND "labName" = '${category}'` : ""}
        ${group ? `AND "analyte_group" = '${group}'` : ""}
        ${
          location
            ? `AND ( "CatchmentArea" = '${location}' OR "GIS_ID" = '${location}' )`
            : ""
        }
        ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
    ),
    current_detected AS (
      SELECT DISTINCT name
      FROM base
      ${
        start_date && end_date
          ? `WHERE sampled_date BETWEEN '${start_date}' AND '${end_date}'`
          : ""
      }
    ),
    previous_detected AS (
      SELECT DISTINCT name
      FROM base
      ${
        comparisonDates?.comparison_start_date &&
        comparisonDates?.comparison_end_date
          ? `WHERE sampled_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}'`
          : ""
      }
    ),
    novel AS (
      SELECT name FROM current_detected
      EXCEPT
      SELECT name FROM previous_detected
    )
    SELECT
      COUNT(*) AS novel_count,
      LIST(name) AS novel_names
    FROM novel;
  `);

          const novelCount = res?.[0]?.novel_count ?? 0;
          // LIST(name) from DuckDB typically comes back as an array; if it comes as a string, normalize it.
          const novelNamesRaw = res?.[0]?.novel_names ?? [];
          const novelNames = Array.isArray(novelNamesRaw)
            ? novelNamesRaw
            : typeof novelNamesRaw === "string"
            ? novelNamesRaw
                .replace(/^[\\[\\]]$/g, "")
                .split(",")
                .map((s) => s.replace(/["\\[\\]]/g, "").trim())
                .filter(Boolean)
            : [];

          return {
            id: 3,
            name: "Novel Analyte Alert",
            type: "two_numbers",
            left_side: {
              main: { value: novelCount, label: "Newly Detected" },
              legend: null,
              list: novelNames.map((n) => ({ analyte_name: n })),
            },
            right_side: null,
            // show the analyte names that are newly detected in the current period
          };
        }

        return {
          disabled: true,
          id: 3,
          name: "Concentration Rate",
          type: "two_numbers",
          left_side: {
            main: { value: 0, label: "-" },
            legend: { type: "change", pct: 0, direction: "-", prefix: "%" },
          },
          right_side: { main: { value: 0, label: "-" } },
          list: null,
        };
      },
      info: {
        id: 3,
        name: "Concentration Trends",
        description: "",
        category: "Analyte Insights",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
        ],
      },
    },
    "4": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        return {
          id: 4,
          name: "High-Change Regions",
          type: "two_numbers",
          left_side: {
            main: {
              value: 0,
              label: "Active",
            },
            legend: {
              type: "change",
              pct: 0,
              direction: "center",
              prefix: "%",
            },
          },
          right_side: {
            main: {
              value: 0,
              label: "Forecast",
            },
            legend: {
              type: "margin",
              amount: 0,
              prefix: "%",
              label: "Uncertainty",
            },
          },
          list: null,
        };
      },
      info: {
        id: 4,
        name: "High-Change Regions",
        description: "",
        category: "Analyte Insights",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
        ],
      },
    },
    // FIXME: WHERE IS THE UI FOR THIS?
    // "5": {
    //   get: async (params: any) => {
    //     const {
    //       start_date,
    //       end_date,
    //       analyte,
    //       category,
    //       group,
    //       location,
    //       sample_type,
    //     } = params;

    //     const res = await query(`
    //       select count(*) as total
    //       from risk_score_info
    //       where monitored is true
    //       `);

    //     return {
    //       id: 5,
    //       name: "Flagged Samples",
    //       type: "single_number",
    //       value: res[0].total,
    //       change: {
    //         pct: 0,
    //         direction: "center",
    //         prefix: "%",
    //       },
    //       legend: "Change",
    //     };
    //   },
    //   info: {
    //     id: 5,
    //     name: "Flagged Samples",
    //     description: "",
    //     category: "Analyte Insights",
    //     parameters: [
    //       "level",
    //       "start_date",
    //       "end_date",
    //       "analyte",
    //       "category",
    //       "group",
    //       "location",
    //     ],
    //   },
    // },
  },
};
