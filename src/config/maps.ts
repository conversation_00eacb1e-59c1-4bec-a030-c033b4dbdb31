import { query } from "../utils/db";
import { query as queryDuck } from "../utils/duckdb";

export const mapsConfig = [
  // sample overview
  {
    collections: ["sample_overview_cards"],
    card_ids: ["1"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame, // possible values: week, month, quarter, year
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
        SELECT
    "SAMPLE_NUMBER",
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    "GIS_ID" AS GIS_ID,
    CASE 
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN 
            strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
            CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
    summary_results_unified
WHERE 1=1
    ${
      start_date && end_date
        ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
        : ""
    }
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
GROUP BY
    "SAMPLE_NUMBER", GIS_ID, DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC

          `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.GIS_ID]) {
            acc[cur.formatted_period].points[cur.GIS_ID] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.GIS_ID].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["2"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
        SELECT
    "SAMPLE_NUMBER",
    "GIS_ID" AS GIS_ID,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE 
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN 
            strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
            CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
    summary_results_unified
WHERE 1=1
and "ResultStatus" = 'A'
    ${
      start_date && end_date
        ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
        : ""
    }
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
    AND "finalresult_value" in  ('Not Detected','Detected')
GROUP BY
    "SAMPLE_NUMBER", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC

          `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.GIS_ID]) {
            acc[cur.formatted_period].points[cur.GIS_ID] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.GIS_ID].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["3"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
        SELECT
    "location",
    "GIS_ID" AS GIS_ID,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE 
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN 
            strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
            CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
    summary_results_unified
WHERE 1=1
    ${
      start_date && end_date
        ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
        : ""
    }
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
GROUP BY
    "location", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC

          `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.GIS_ID]) {
            acc[cur.formatted_period].points[cur.GIS_ID] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.GIS_ID].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["4"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
        SELECT
    "sampled_date",
    "GIS_ID" AS GIS_ID,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE 
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN 
            strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
            CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
    summary_results_unified
WHERE 1=1
    ${
      start_date && end_date
        ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
        : ""
    }
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
GROUP BY
    "sampled_date", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC

          `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.GIS_ID]) {
            acc[cur.formatted_period].points[cur.GIS_ID] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.GIS_ID].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["6"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
        SELECT
    "SAMPLE_NUMBER",
    "GIS_ID" AS GIS_ID,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE 
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN 
            strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
            CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
    summary_results_unified
WHERE 1=1
    ${
      start_date && end_date
        ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
        : ""
    }
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
    and "SampleStatus" not in ('U')
GROUP BY
    "SAMPLE_NUMBER", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC

          `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.GIS_ID]) {
            acc[cur.formatted_period].points[cur.GIS_ID] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.GIS_ID].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["7"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
        SELECT
    "SAMPLE_NUMBER",
    "GIS_ID" AS GIS_ID,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE 
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN 
            strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
            CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
    summary_results_unified
WHERE 1=1
    ${
      start_date && end_date
        ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
        : ""
    }
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
    and "SampleStatus" = 'I'
GROUP BY
    "SAMPLE_NUMBER", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC

          `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.GIS_ID]) {
            acc[cur.formatted_period].points[cur.GIS_ID] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.GIS_ID].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["8"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
        SELECT
    "SAMPLE_NUMBER",
    "GIS_ID" AS GIS_ID,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE 
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN 
            strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
            CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
    summary_results_unified
WHERE 1=1
    ${
      start_date && end_date
        ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
        : ""
    }
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
    and "finalresult_value" =  'Detected'
GROUP BY
    "SAMPLE_NUMBER", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC

          `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.GIS_ID]) {
            acc[cur.formatted_period].points[cur.GIS_ID] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.GIS_ID].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["9"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
        SELECT
    "SAMPLE_NUMBER",
    "GIS_ID" AS GIS_ID,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE 
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN 
            strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
            CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
    summary_results_unified
WHERE 1=1
    ${
      start_date && end_date
        ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
        : ""
    }
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
    and "finalresult_value" =  'Not Detected'
    and "ResultStatus" = 'A'
GROUP BY
    "SAMPLE_NUMBER", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC

          `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.GIS_ID]) {
            acc[cur.formatted_period].points[cur.GIS_ID] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.GIS_ID].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["10"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
        SELECT
    "SAMPLE_NUMBER",
    "GIS_ID" AS GIS_ID,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE 
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN 
            strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
            CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
    summary_results_unified
WHERE 1=1
    ${
      start_date && end_date
        ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
        : ""
    }
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
    and "SampleStatus" = 'U'
GROUP BY
    "SAMPLE_NUMBER", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC

          `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.GIS_ID]) {
            acc[cur.formatted_period].points[cur.GIS_ID] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.GIS_ID].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["11"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame, // possible values: week, month, quarter, year
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
      SELECT
  "SAMPLE_NUMBER",
  "GIS_ID" AS gis_id,
  DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
  CASE 
      WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
      WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
      WHEN '${dateTrunc}' = 'quarter' THEN 
          strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
          CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
      WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
      ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
  END AS formatted_period
FROM
  summary_results_unified
WHERE 1=1
and "TestStatus" = 'I'

  ${
    start_date && end_date
      ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
      : ""
  }
  ${
    location
      ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
      : ""
  }
  ${analyte ? `AND "name" = '${analyte}'` : ""}
  ${group ? `AND "analyte_group" = '${group}'` : ""}
  ${category ? `AND "labName" = '${category}'` : ""}
  ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

GROUP BY
  "SAMPLE_NUMBER", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
  time_period ASC

        `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.gis_id]) {
            acc[cur.formatted_period].points[cur.gis_id] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.gis_id].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["12"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame, // possible values: week, month, quarter, year
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
      SELECT
  "SAMPLE_NUMBER",
  "GIS_ID" AS gis_id,
  DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
  CASE 
      WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
      WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
      WHEN '${dateTrunc}' = 'quarter' THEN 
          strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
          CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
      WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
      ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
  END AS formatted_period
FROM
  summary_results_unified
WHERE 1=1
and "ResultStatus" = 'A'

  ${
    start_date && end_date
      ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
      : ""
  }
  ${
    location
      ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
      : ""
  }
  ${analyte ? `AND "name" = '${analyte}'` : ""}
  ${group ? `AND "analyte_group" = '${group}'` : ""}
  ${category ? `AND "labName" = '${category}'` : ""}
  ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

GROUP BY
  "SAMPLE_NUMBER", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
  time_period ASC

        `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.gis_id]) {
            acc[cur.formatted_period].points[cur.gis_id] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.gis_id].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["13"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame, // possible values: week, month, quarter, year
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
      SELECT
  "SAMPLE_NUMBER",
  "GIS_ID" AS gis_id,
  DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
  CASE 
      WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
      WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
      WHEN '${dateTrunc}' = 'quarter' THEN 
          strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
          CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
      WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
      ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
  END AS formatted_period
FROM
  summary_results_unified
WHERE 1=1
and "ResultStatus" <> 'A'

  ${
    start_date && end_date
      ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
      : ""
  }
  ${
    location
      ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
      : ""
  }
  ${analyte ? `AND "name" = '${analyte}'` : ""}
  ${group ? `AND "analyte_group" = '${group}'` : ""}
  ${category ? `AND "labName" = '${category}'` : ""}
  ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

GROUP BY
  "SAMPLE_NUMBER", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
  time_period ASC

        `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.gis_id]) {
            acc[cur.formatted_period].points[cur.gis_id] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.gis_id].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["14"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame, // possible values: week, month, quarter, year
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
      SELECT
  "SAMPLE_NUMBER",
  "GIS_ID" AS gis_id,
  DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
  CASE 
      WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
      WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
      WHEN '${dateTrunc}' = 'quarter' THEN 
          strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
          CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
      WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
      ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
  END AS formatted_period
FROM
  summary_results_unified
WHERE 1=1
and "finalresult_value" =  'Not Detected'
and "ResultStatus" = 'A'

  ${
    start_date && end_date
      ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
      : ""
  }
  ${
    location
      ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
      : ""
  }
  ${analyte ? `AND "name" = '${analyte}'` : ""}
  ${group ? `AND "analyte_group" = '${group}'` : ""}
  ${category ? `AND "labName" = '${category}'` : ""}
  ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

GROUP BY
  "SAMPLE_NUMBER", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
  time_period ASC

        `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.gis_id]) {
            acc[cur.formatted_period].points[cur.gis_id] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.gis_id].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["16"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame, // possible values: week, month, quarter, year
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
      SELECT
  "SAMPLE_NUMBER",
  "GIS_ID" AS gis_id,
  DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
  CASE 
      WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
      WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
      WHEN '${dateTrunc}' = 'quarter' THEN 
          strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
          CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
      WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
      ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
  END AS formatted_period
FROM
  summary_results_unified
WHERE 1=1
and "finalresult_value" =  'Detected'
and "ResultStatus" = 'A'

  ${
    start_date && end_date
      ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
      : ""
  }
  ${
    location
      ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
      : ""
  }
  ${analyte ? `AND "name" = '${analyte}'` : ""}
  ${group ? `AND "analyte_group" = '${group}'` : ""}
  ${category ? `AND "labName" = '${category}'` : ""}
  ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

GROUP BY
  "SAMPLE_NUMBER", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
  time_period ASC

        `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.gis_id]) {
            acc[cur.formatted_period].points[cur.gis_id] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.gis_id].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["20"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame, // possible values: week, month, quarter, year
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
      SELECT
 "Test_Number" , 
  "GIS_ID" AS gis_id,
  DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
  CASE 
      WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
      WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
      WHEN '${dateTrunc}' = 'quarter' THEN 
          strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
          CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
      WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
      ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
  END AS formatted_period
FROM
  summary_results_unified
WHERE 1=1
and "ResultStatus" =  'A'
${
  start_date && end_date
    ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
  ${
    location
      ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
      : ""
  }
  ${analyte ? `AND "name" = '${analyte}'` : ""}
  ${group ? `AND "analyte_group" = '${group}'` : ""}
  ${category ? `AND "labName" = '${category}'` : ""}
  ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

GROUP BY
  "Test_Number", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
  time_period ASC

        `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.gis_id]) {
            acc[cur.formatted_period].points[cur.gis_id] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.gis_id].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["21"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame, // possible values: week, month, quarter, year
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
      SELECT
  "SAMPLE_NUMBER",
  "GIS_ID" AS gis_id,
  DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
  CASE 
      WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
      WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
      WHEN '${dateTrunc}' = 'quarter' THEN 
          strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
          CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
      WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
      ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
  END AS formatted_period
FROM
  summary_results_unified
WHERE 1=1
and "SampleStatus" =  'R'

  ${
    start_date && end_date
      ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
      : ""
  }
  ${
    location
      ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
      : ""
  }
  ${analyte ? `AND "name" = '${analyte}'` : ""}
  ${group ? `AND "analyte_group" = '${group}'` : ""}
  ${category ? `AND "labName" = '${category}'` : ""}
  ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

GROUP BY
  "SAMPLE_NUMBER", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
  time_period ASC

        `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.gis_id]) {
            acc[cur.formatted_period].points[cur.gis_id] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.gis_id].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["22"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame, // possible values: week, month, quarter, year
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
      SELECT
  "SAMPLE_NUMBER",
  "GIS_ID" AS gis_id,
  DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
  CASE 
      WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
      WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
      WHEN '${dateTrunc}' = 'quarter' THEN 
          strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
          CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
      WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
      ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
  END AS formatted_period
FROM
  summary_results_unified
WHERE 1=1
and "SampleStatus" =  'X'

  ${
    start_date && end_date
      ? `AND ("sampled_date" BETWEEN '${start_date}' AND '${end_date}')`
      : ""
  }
  ${
    location
      ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
      : ""
  }
  ${analyte ? `AND "name" = '${analyte}'` : ""}
  ${group ? `AND "analyte_group" = '${group}'` : ""}
  ${category ? `AND "labName" = '${category}'` : ""}
  ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

GROUP BY
  "SAMPLE_NUMBER", "GIS_ID", DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
  time_period ASC

        `);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.gis_id]) {
            acc[cur.formatted_period].points[cur.gis_id] = {
              weight: 0,
            };
          }

          acc[cur.formatted_period].points[cur.gis_id].weight += 1;
          return acc;
        }, {})
      );

      return {
        type: "single_value",
        map: data,
      };
    },
  },
  // analyte insights
  {
    collections: ["analyte_insights_cards"],
    card_ids: ["*"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        analyte,
        category,
        group,
        location,
        sample_type,
        date_frame,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
       WITH base AS (
  SELECT
    s.*,
    l.loq_value
  FROM summary_results_unified s
  LEFT JOIN analyte_loq_clean l
    ON s.name = l.Analyte
  WHERE "ResultStatus" = 'A'
    ${
      location
        ? `AND ( "CatchmentArea" = '${location}' OR "GIS_ID" = '${location}' )`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
),
dedup AS (
  -- keep ONE test per analyte per sample: the latest by DATE_COMPLETED
  SELECT
    b.*,
    ROW_NUMBER() OVER (
      PARTITION BY b."SAMPLE_NUMBER", b."name"
      ORDER BY b."DATE_COMPLETED" DESC
    ) AS rn
  FROM base b
),
latest AS (
  SELECT * FROM dedup WHERE rn = 1
),
tagged AS (
  -- compute detection on the kept row
  SELECT
    l.*,
    CASE
      -- Special case: RADIOACTIVITY → trust FinalResult_Value only
      WHEN l."ANALYSIS" = 'RADIOACTIVITY' THEN
        (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))

      -- Numeric rawResult + LOQ available
      WHEN TRY_CAST(l."rawResult" AS DOUBLE) IS NOT NULL AND l.loq_value IS NOT NULL THEN
        CASE
          WHEN CAST(l."rawResult" AS DOUBLE) > l.loq_value THEN TRUE
          ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
        END

      -- Fallback to FinalResult_Value
      ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
    END AS is_detected
  FROM latest l
),
classified AS (
  SELECT
    "SAMPLE_NUMBER",
    sampled_date,
    "GIS_ID",
    "name",
    "analyte_group",
    "labName",
    "sample_type",
    CASE WHEN is_detected THEN 'Detected' ELSE 'Not Detected' END AS finalresult_value
  FROM tagged
  WHERE 1=1
    ${
      start_date && end_date
        ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
        : ""
    }
)
SELECT 
  "GIS_ID" AS catchment_name,
  DATE_TRUNC('${dateTrunc}', sampled_date) AS time_period,
  CASE 
    WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y-W%V')
    WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y-%m')
    WHEN '${dateTrunc}' = 'quarter' THEN 
      strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y') || '-Q' ||
      CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', sampled_date)) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
    WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y')
    ELSE strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y-%m')
  END AS formatted_period,

  -- One test per analyte per sample (post-dedup), now counted by detection class
  COUNT(*) FILTER (WHERE finalresult_value = 'Detected')      AS period_present_analytes,
  COUNT(*) FILTER (WHERE finalresult_value = 'Not Detected')  AS period_absent_analytes

FROM classified
GROUP BY 
  "GIS_ID", DATE_TRUNC('${dateTrunc}', sampled_date)
ORDER BY 
  time_period ASC;

`);

      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.catchment_name]) {
            acc[cur.formatted_period].points[cur.catchment_name] = {
              detected_count: 0,
              n_detected_count: 0,
            };
          }

          acc[cur.formatted_period].points[cur.catchment_name].detected_count +=
            +cur.period_present_analytes;
          acc[cur.formatted_period].points[
            cur.catchment_name
          ].n_detected_count += +cur.period_absent_analytes;
          return acc;
        }, {})
      );

      return {
        type: "two_values",
        map: data,
      };
    },
  },
  // risk trends
  {
    collections: ["risk_trends_cards"],
    card_ids: ["1"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        analyte,
        category,
        group,
        location,
        sample_type, // not used (kept for signature compatibility)
        date_frame,
        risks,
        persona,
        historical,
      } = params;

      const risk_score_id = risks ? risks.split(",") : [];

      // Align persona parsing with the card logic
      const personaList = persona
        ? persona
            .split(",")
            ?.filter(Boolean)
            ?.map((p: string) => p.trim())
            ?.filter(Boolean)
        : [];

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      // Use the same table, join, and filters as the card.
      // Time grouping is based on the *latest known date* for the risk,
      // but the filter matches the card: first OR last date within the window.
      const res = await queryDuck(`
    SELECT 
      GIS_ID AS catchment_area,
      time_period,
      CASE
    WHEN '${dateTrunc}' = 'quarter' THEN 
      CONCAT(
        CAST(EXTRACT(YEAR FROM time_period) AS VARCHAR),
        '-Q',
        CAST(EXTRACT(QUARTER FROM time_period) AS VARCHAR)
      )
    WHEN '${dateTrunc}' = 'week' THEN 
      strftime(time_period, '%G-W%V')
    WHEN '${dateTrunc}' = 'month' THEN 
      strftime(time_period, '%Y-%m')
    WHEN '${dateTrunc}' = 'year' THEN 
      strftime(time_period, '%Y')
    ELSE 
      strftime(time_period, '%Y-%m')
  END AS formatted_period,

      ${
        (risk_score_id?.length ?? 0) > 0
          ? risk_score_id
              .map(
                (i: string) =>
                  `COUNT(*) FILTER (WHERE last_risk_category_id = ${Number(
                    i
                  )}) AS risk_score_${Number(i)}`
              )
              .join(",")
          : `COUNT(*) FILTER (WHERE last_risk_category_id = 1) AS risk_score_1,
             COUNT(*) FILTER (WHERE last_risk_category_id = 2) AS risk_score_2,
             COUNT(*) FILTER (WHERE last_risk_category_id = 3) AS risk_score_3,
             COUNT(*) FILTER (WHERE last_risk_category_id = 4) AS risk_score_4,
             COUNT(*) FILTER (WHERE last_risk_category_id = 5) AS risk_score_5`
      }
    FROM (
      SELECT
        rs.GIS_ID,
        DATE_TRUNC('${dateTrunc}', COALESCE(rs.last_risk_date::timestamp, rs.first_risk_date::timestamp)) AS time_period,
        rs.last_risk_category_id
      FROM risk_score_info_summaries rs
      LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
      WHERE 1=1
        AND rs.lastGroup = true
        AND rs.risk_count > 1  ${
          historical ? "" : " and first_risk_date >= '2025-05-01' "
        } 
        ${
          start_date && end_date
            ? `
              AND (
                rs.first_risk_date BETWEEN '${start_date}' AND '${end_date}'
                OR
                rs.last_risk_date BETWEEN '${start_date}' AND '${end_date}'
              )
            `
            : ""
        }
        ${location ? `AND rs.GIS_ID = '${location}'` : ""}
        ${analyte ? `AND da.name = '${analyte}'` : ""}
        ${category ? `AND da.labName = '${category}'` : ""}
        ${group ? `AND da.Analyte_Group = '${group}'` : ""}
        ${
          personaList && personaList.length
            ? `AND da.persona IN (${personaList
                .map((p: string) => `'${p.replace(/'/g, "''")}'`)
                .join(",")})`
            : ""
        }
        ${
          risk_score_id && risk_score_id.length
            ? `AND rs.last_risk_category_id IN (${risk_score_id
                .map((r: string) => Number(r))
                .join(",")})`
            : ""
        }
    ) AS sub
    GROUP BY GIS_ID, time_period
    ORDER BY time_period ASC;
  `);

      // Keep your shaping logic as-is, defaulting to 1..5 buckets when not provided
      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          const risksList =
            risk_score_id && risk_score_id.length > 0
              ? risk_score_id.map((r: string) => Number(r))
              : [1, 2, 3, 4, 5];

          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.catchment_area]) {
            acc[cur.formatted_period].points[cur.catchment_area] = {};
            risksList.forEach((risk: number) => {
              acc[cur.formatted_period].points[cur.catchment_area][
                `risk_score_${risk}`
              ] = 0;
            });
          }

          risksList.forEach((risk: number) => {
            acc[cur.formatted_period].points[cur.catchment_area][
              `risk_score_${risk}`
            ] += +cur[`risk_score_${risk}`];
          });

          return acc;
        }, {})
      );

      return {
        type: "two_values",
        map: data,
      };
    },
  },
  {
    collections: ["risk_trends_cards"],
    card_ids: ["2"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        analyte,
        category,
        group,
        location,
        sample_type, // not used (kept for signature compatibility)
        date_frame,
        risks,
        persona,
        historical,
      } = params;

      const risk_score_id = risks ? risks.split(",") : [];

      // Align persona parsing with the card logic
      const personaList = persona
        ? persona
            .split(",")
            ?.filter(Boolean)
            ?.map((p: string) => p.trim())
            ?.filter(Boolean)
        : [];

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      // Use the same table, join, and filters as the card.
      // Time grouping is based on the *latest known date* for the risk,
      // but the filter matches the card: first OR last date within the window.
      const res = await queryDuck(`
    SELECT 
      GIS_ID AS catchment_area,
      time_period,
       CASE
    WHEN '${dateTrunc}' = 'quarter' THEN 
      CONCAT(
        CAST(EXTRACT(YEAR FROM time_period) AS VARCHAR),
        '-Q',
        CAST(EXTRACT(QUARTER FROM time_period) AS VARCHAR)
      )
    WHEN '${dateTrunc}' = 'week' THEN 
      strftime(time_period, '%G-W%V')
    WHEN '${dateTrunc}' = 'month' THEN 
      strftime(time_period, '%Y-%m')
    WHEN '${dateTrunc}' = 'year' THEN 
      strftime(time_period, '%Y')
    ELSE 
      strftime(time_period, '%Y-%m')
  END AS formatted_period,
      ${
        (risk_score_id?.length ?? 0) > 0
          ? risk_score_id
              .map(
                (i: string) =>
                  `COUNT(*) FILTER (WHERE last_risk_category_id = ${Number(
                    i
                  )}) AS risk_score_${Number(i)}`
              )
              .join(",")
          : `COUNT(*) FILTER (WHERE last_risk_category_id = 1) AS risk_score_1,
             COUNT(*) FILTER (WHERE last_risk_category_id = 2) AS risk_score_2,
             COUNT(*) FILTER (WHERE last_risk_category_id = 3) AS risk_score_3,
             COUNT(*) FILTER (WHERE last_risk_category_id = 4) AS risk_score_4,
             COUNT(*) FILTER (WHERE last_risk_category_id = 5) AS risk_score_5`
      }
    FROM (
      SELECT
        rs.GIS_ID,
        DATE_TRUNC('${dateTrunc}', COALESCE(rs.last_risk_date::timestamp, rs.first_risk_date::timestamp)) AS time_period,
        rs.last_risk_category_id
      FROM risk_score_info_summaries rs
      LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
      WHERE 1=1
        AND rs.lastGroup = true
        AND rs.risk_count > 1  ${
          historical ? "" : " and first_risk_date >= '2025-05-01' "
        } 
        and emerging_risk_flag = true
        ${
          start_date && end_date
            ? `
              AND (
                rs.first_risk_date BETWEEN '${start_date}' AND '${end_date}'
                OR
                rs.last_risk_date BETWEEN '${start_date}' AND '${end_date}'
              )
            `
            : ""
        }
             ${location ? `AND rs.GIS_ID = '${location}'` : ""}
        ${analyte ? `AND da.name = '${analyte}'` : ""}
        ${category ? `AND da.labName = '${category}'` : ""}
        ${group ? `AND da.Analyte_Group = '${group}'` : ""}
        ${
          personaList && personaList.length
            ? `AND da.persona IN (${personaList
                .map((p: string) => `'${p.replace(/'/g, "''")}'`)
                .join(",")})`
            : ""
        }
        ${
          risk_score_id && risk_score_id.length
            ? `AND rs.last_risk_category_id IN (${risk_score_id
                .map((r: string) => Number(r))
                .join(",")})`
            : ""
        }
    ) AS sub
    GROUP BY GIS_ID, time_period
    ORDER BY time_period ASC;
  `);

      // Keep your shaping logic as-is, defaulting to 1..5 buckets when not provided
      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          const risksList =
            risk_score_id && risk_score_id.length > 0
              ? risk_score_id.map((r: string) => Number(r))
              : [1, 2, 3, 4, 5];

          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.catchment_area]) {
            acc[cur.formatted_period].points[cur.catchment_area] = {};
            risksList.forEach((risk: number) => {
              acc[cur.formatted_period].points[cur.catchment_area][
                `risk_score_${risk}`
              ] = 0;
            });
          }

          risksList.forEach((risk: number) => {
            acc[cur.formatted_period].points[cur.catchment_area][
              `risk_score_${risk}`
            ] += +cur[`risk_score_${risk}`];
          });

          return acc;
        }, {})
      );
      return {
        type: "two_values",
        map: data,
      };
    },
  },
  {
    collections: ["risk_trends_cards"],
    card_ids: ["3"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        analyte,
        category,
        group,
        location,
        sample_type, // not used (kept for signature compatibility)
        date_frame,
        risks,
        persona,
        historical,
      } = params;

      const risk_score_id = risks ? risks.split(",") : [];

      // Align persona parsing with the card logic
      const personaList = persona
        ? persona
            .split(",")
            ?.filter(Boolean)
            ?.map((p: string) => p.trim())
            ?.filter(Boolean)
        : [];

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      // Use the same table, join, and filters as the card.
      // Time grouping is based on the *latest known date* for the risk,
      // but the filter matches the card: first OR last date within the window.
      const res = await queryDuck(`
    SELECT 
      GIS_ID AS catchment_area,
      time_period,
        CASE
    WHEN '${dateTrunc}' = 'quarter' THEN 
      CONCAT(
        CAST(EXTRACT(YEAR FROM time_period) AS VARCHAR),
        '-Q',
        CAST(EXTRACT(QUARTER FROM time_period) AS VARCHAR)
      )
    WHEN '${dateTrunc}' = 'week' THEN 
      strftime(time_period, '%G-W%V')
    WHEN '${dateTrunc}' = 'month' THEN 
      strftime(time_period, '%Y-%m')
    WHEN '${dateTrunc}' = 'year' THEN 
      strftime(time_period, '%Y')
    ELSE 
      strftime(time_period, '%Y-%m')
  END AS formatted_period,
      ${
        (risk_score_id?.length ?? 0) > 0
          ? risk_score_id
              .map(
                (i: string) =>
                  `COUNT(*) FILTER (WHERE last_risk_category_id = ${Number(
                    i
                  )}) AS risk_score_${Number(i)}`
              )
              .join(",")
          : `COUNT(*) FILTER (WHERE last_risk_category_id = 1) AS risk_score_1,
             COUNT(*) FILTER (WHERE last_risk_category_id = 2) AS risk_score_2,
             COUNT(*) FILTER (WHERE last_risk_category_id = 3) AS risk_score_3,
             COUNT(*) FILTER (WHERE last_risk_category_id = 4) AS risk_score_4,
             COUNT(*) FILTER (WHERE last_risk_category_id = 5) AS risk_score_5`
      }
    FROM (
      SELECT
        rs.GIS_ID,
        DATE_TRUNC('${dateTrunc}', COALESCE(rs.last_risk_date::timestamp, rs.first_risk_date::timestamp)) AS time_period,
        rs.last_risk_category_id
      FROM risk_score_info_summaries rs
      LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
      WHERE 1=1
        AND rs.lastGroup = true
        AND rs.risk_count > 1  ${
          historical ? "" : " and first_risk_date >= '2025-05-01' "
        } 
        and da.labName = 'Radioactivity' 
        ${
          start_date && end_date
            ? `
              AND (
                rs.first_risk_date BETWEEN '${start_date}' AND '${end_date}'
                OR
                rs.last_risk_date BETWEEN '${start_date}' AND '${end_date}'
              )
            `
            : ""
        }
        ${location ? `AND rs.GIS_ID = '${location}'` : ""}
        ${analyte ? `AND da.name = '${analyte}'` : ""}
        ${category ? `AND da.labName = '${category}'` : ""}
        ${group ? `AND da.Analyte_Group = '${group}'` : ""}
        ${
          personaList && personaList.length
            ? `AND da.persona IN (${personaList
                .map((p: string) => `'${p.replace(/'/g, "''")}'`)
                .join(",")})`
            : ""
        }
        ${
          risk_score_id && risk_score_id.length
            ? `AND rs.last_risk_category_id IN (${risk_score_id
                .map((r: string) => Number(r))
                .join(",")})`
            : ""
        }
    ) AS sub
    GROUP BY GIS_ID, time_period
    ORDER BY time_period ASC;
  `);

      // Keep your shaping logic as-is, defaulting to 1..5 buckets when not provided
      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          const risksList =
            risk_score_id && risk_score_id.length > 0
              ? risk_score_id.map((r: string) => Number(r))
              : [1, 2, 3, 4, 5];

          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.catchment_area]) {
            acc[cur.formatted_period].points[cur.catchment_area] = {};
            risksList.forEach((risk: number) => {
              acc[cur.formatted_period].points[cur.catchment_area][
                `risk_score_${risk}`
              ] = 0;
            });
          }

          risksList.forEach((risk: number) => {
            acc[cur.formatted_period].points[cur.catchment_area][
              `risk_score_${risk}`
            ] += +cur[`risk_score_${risk}`];
          });

          return acc;
        }, {})
      );

      return {
        type: "two_values",
        map: data,
      };
    },
  },
  {
    collections: ["risk_trends_cards"],
    card_ids: ["4"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        analyte,
        category,
        group,
        location,
        sample_type, // not used (kept for signature compatibility)
        date_frame,
        risks,
        persona,
        historical,
      } = params;

      const risk_score_id = risks ? risks.split(",") : [];

      // Align persona parsing with the card logic
      const personaList = persona
        ? persona
            .split(",")
            ?.filter(Boolean)
            ?.map((p: string) => p.trim())
            ?.filter(Boolean)
        : [];

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      // Use the same table, join, and filters as the card.
      // Time grouping is based on the *latest known date* for the risk,
      // but the filter matches the card: first OR last date within the window.
      const res = await queryDuck(`
    SELECT 
      GIS_ID AS catchment_area,
      time_period,
       CASE
    WHEN '${dateTrunc}' = 'quarter' THEN 
      CONCAT(
        CAST(EXTRACT(YEAR FROM time_period) AS VARCHAR),
        '-Q',
        CAST(EXTRACT(QUARTER FROM time_period) AS VARCHAR)
      )
    WHEN '${dateTrunc}' = 'week' THEN 
      strftime(time_period, '%G-W%V')
    WHEN '${dateTrunc}' = 'month' THEN 
      strftime(time_period, '%Y-%m')
    WHEN '${dateTrunc}' = 'year' THEN 
      strftime(time_period, '%Y')
    ELSE 
      strftime(time_period, '%Y-%m')
  END AS formatted_period,
      0 AS risk_score_1,
      0 AS risk_score_2,
      0 AS risk_score_3,
      0 AS risk_score_4,
      COUNT(distinct GIS_ID) FILTER (WHERE last_risk_category_id = 5) AS risk_score_5
    FROM (
      SELECT
        rs.GIS_ID,
        DATE_TRUNC('${dateTrunc}', COALESCE(rs.last_risk_date::timestamp, rs.first_risk_date::timestamp)) AS time_period,
        rs.last_risk_category_id
      FROM risk_score_info_summaries rs
      LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
      WHERE 1=1
        AND rs.lastGroup = true
        AND rs.risk_count > 1  ${
          historical ? "" : " and first_risk_date >= '2025-05-01' "
        } 
        and rs.last_risk_category_id = '5'

        ${
          start_date && end_date
            ? `
              AND (
                rs.first_risk_date BETWEEN '${start_date}' AND '${end_date}'
                OR
                rs.last_risk_date BETWEEN '${start_date}' AND '${end_date}'
              )
            `
            : ""
        }
        ${location ? `AND rs.GIS_ID = '${location}'` : ""}
        ${analyte ? `AND da.name = '${analyte}'` : ""}
        ${category ? `AND da.labName = '${category}'` : ""}
        ${group ? `AND da.Analyte_Group = '${group}'` : ""}
        ${
          personaList && personaList.length
            ? `AND da.persona IN (${personaList
                .map((p: string) => `'${p.replace(/'/g, "''")}'`)
                .join(",")})`
            : ""
        }
        ${
          risk_score_id && risk_score_id.length
            ? `AND rs.last_risk_category_id IN (${risk_score_id
                .map((r: string) => Number(r))
                .join(",")})`
            : ""
        }
    ) AS sub
    GROUP BY GIS_ID, time_period
    ORDER BY time_period ASC;
  `);

      // Keep your shaping logic as-is, defaulting to 1..5 buckets when not provided
      const data = Object.values(
        res.reduce((acc: any, cur: any) => {
          const risksList =
            risk_score_id && risk_score_id.length > 0
              ? risk_score_id.map((r: string) => Number(r))
              : [1, 2, 3, 4, 5];

          if (!acc[cur.formatted_period]) {
            acc[cur.formatted_period] = {
              date: cur.formatted_period,
              points: {},
            };
          }

          if (!acc[cur.formatted_period].points[cur.catchment_area]) {
            acc[cur.formatted_period].points[cur.catchment_area] = {};
            risksList.forEach((risk: number) => {
              acc[cur.formatted_period].points[cur.catchment_area][
                `risk_score_${risk}`
              ] = 0;
            });
          }

          risksList.forEach((risk: number) => {
            acc[cur.formatted_period].points[cur.catchment_area][
              `risk_score_${risk}`
            ] += +cur[`risk_score_${risk}`];
          });

          return acc;
        }, {})
      );

      return {
        type: "two_values",
        map: data,
      };
    },
  },
];
