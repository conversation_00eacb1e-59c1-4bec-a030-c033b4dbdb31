// import { query } from "../utils/db";
import cards_query_info from "./query-data";
import { query } from "../utils/db";
import { query as queryDuck } from "../utils/duckdb";

export const plotsConfig = [
  // sample_overview_cards
  // 1
  {
    collections: ["sample_overview_cards"],
    card_ids: ["1"],
    title: "Samples Collected Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
  SELECT
    COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    "GIS_ID",
    CASE
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
             CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
    summary_results_unified
WHERE
    1 = 1
    ${
      start_date && end_date
        ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
        : ""
    }
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
            
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
GROUP BY
    DATE_TRUNC('${dateTrunc}', "sampled_date"), "GIS_ID"
ORDER BY
    time_period ASC
          `);
      return {
        id: "2",
        title: `Samples Collected Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Samples Collected",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["1"],
    title: "Samples Collected By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
          SELECT
            COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
            "location" as location_name,
            "GIS_ID"

            FROM summary_results_unified
            where 1=1 
        ${
          start_date && end_date
            ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
            : ""
        }
         ${
           location
             ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
             : ""
         }
         ${analyte ? `AND "name" = '${analyte}'` : ""}
         ${group ? `AND "analyte_group" = '${group}'` : ""}
         ${category ? `AND "labName" = '${category}'` : ""}
         ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
        group by "location", "GIS_ID"
        order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
        limit 10
        `);

      return {
        id: "1",
        title: "Samples Collected By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.GIS_ID,
          },
          x_label: "Location",
          y1_label: "Samples Collected",
        })),
      };
    },
  },

  //2
  {
    collections: ["sample_overview_cards"],
    card_ids: ["2"],
    title: "Total Samples Tested By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
          SELECT
            COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
            "location" as location_name,
            "GIS_ID"
            FROM summary_results_unified
            summary_results_unified
            WHERE 1=1
            and "ResultStatus" = 'A'
          ${
            start_date && end_date
              ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
              : ""
          }
         ${
           location
             ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
             : ""
         }
         ${analyte ? `AND "name" = '${analyte}'` : ""}
        ${group ? `AND "analyte_group" = '${group}'` : ""}
        ${category ? `AND "labName" = '${category}'` : ""}
        ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
        group by "location", GIS_ID
        order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
        limit 10
        `);

      return {
        id: "3",
        title: "Total Samples Tested By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.GIS_ID,
          },
        })),
        x_label: "Location",
        y1_label: "Total Sample Count",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["2"],
    title: "Total Samples Tested Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
  SELECT
    COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
             CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
summary_results_unified
WHERE 1=1
and "ResultStatus" = 'A'
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
GROUP BY
    DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC
          `);
      return {
        id: "4",
        title: `Total Samples Tested Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Total Samples Tested",
      };
    },
  },

  // 3
  {
    collections: ["sample_overview_cards"],
    card_ids: ["3"],
    title: "Total Number Of Collection Points By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
          SELECT
            COUNT(DISTINCT "location") as count , 
            "location" as location_name,
            "GIS_ID"
            FROM summary_results_unified
            summary_results_unified
            WHERE 1=1
            ${
              start_date && end_date
                ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
                : ""
            }
         ${
           location
             ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
             : ""
         }
         ${analyte ? `AND "name" = '${analyte}'` : ""}
         ${group ? `AND "analyte_group" = '${group}'` : ""}
         ${category ? `AND "labName" = '${category}'` : ""}
         ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
        group by "location", GIS_ID
        order by COUNT(DISTINCT "location") desc
        limit 10
        `);

      return {
        id: "5",
        title: "Total Number of Collection Points By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.GIS_ID,
          },
        })),
        x_label: "Location",
        y1_label: "Total Number Of Collection Points",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["3"],
    title: "Total Number of Collection Points Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
  SELECT
    COUNT(DISTINCT "sampled_date") AS count,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
             CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
summary_results_unified
WHERE 1=1
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
GROUP BY
    DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC
          `);
      return {
        id: "8",
        title: `Total Number of Collection Points Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Total Number Of Collection Points",
      };
    },
  },

  // 4
  {
    collections: ["sample_overview_cards"],
    card_ids: ["4"],
    title: "Total Days of Sample Collection By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
          SELECT
            COUNT(DISTINCT "sampled_date") as count , 
            "location" as location_name,
            "GIS_ID"
            FROM summary_results_unified
            summary_results_unified
            WHERE 1=1
          ${
            start_date && end_date
              ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
              : ""
          }
         ${
           location
             ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
             : ""
         }
         ${analyte ? `AND "name" = '${analyte}'` : ""}
         ${group ? `AND "analyte_group" = '${group}'` : ""}
         ${category ? `AND "labName" = '${category}'` : ""}
         ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
        group by "location", GIS_ID
        order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
        limit 10
        `);

      return {
        id: "7",
        title: "Total Days of Sample Collection By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.GIS_ID,
          },
        })),
        x_label: "Location",
        y1_label: "Total Days of Sample Collection",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["4"],
    title: "Total Days of Sample Collection Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
  SELECT
    COUNT(DISTINCT "sampled_date") AS count,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
             CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
summary_results_unified
WHERE 1=1
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
GROUP BY
    DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC
          `);
      return {
        id: "8",
        title: `Total Days of Sample Collection Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Total Days of Sample Collection",
      };
    },
  },

  // 6
  {
    collections: ["sample_overview_cards"],
    card_ids: ["6"],
    title: "Total Number of Samples Received By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
          SELECT
            COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
            "location" as location_name,
            "GIS_ID"
            FROM summary_results_unified
            summary_results_unified
            WHERE 1=1
              and "SampleStatus" not in ('U')
          ${
            start_date && end_date
              ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
              : ""
          }
         ${
           location
             ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
             : ""
         }
         ${analyte ? `AND "name" = '${analyte}'` : ""}
         ${group ? `AND "analyte_group" = '${group}'` : ""}
         ${category ? `AND "labName" = '${category}'` : ""}
         ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
        group by "location", "GIS_ID"
        order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
        limit 10
        `);

      return {
        id: "11",
        title: "Total Number of Samples Received By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.GIS_ID,
          },
        })),
        x_label: "location",
        y1_label: "Total Number of Samples Received",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["6"],
    title: "Total Number of Samples Received Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
  SELECT
    COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
             CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
summary_results_unified
WHERE 1=1
and "SampleStatus" not in ('U')
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
GROUP BY
    DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC
          `);
      return {
        id: "12",
        title: `Total Number of Samples Received Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Total Number of Samples Received",
      };
    },
  },

  // 7
  {
    collections: ["sample_overview_cards"],
    card_ids: ["7"],
    title: "Samples to Test By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
          SELECT
            COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
            "location" as location_name,
            "GIS_ID"
            FROM summary_results_unified
            summary_results_unified
            WHERE 1=1
              and "SampleStatus" = 'I'
          ${
            start_date && end_date
              ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
              : ""
          }
         ${
           location
             ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
             : ""
         }
         ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
        group by "location", "GIS_ID"
        order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
        limit 10
        `);

      return {
        id: "13",
        title: "Samples to Test By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.GIS_ID,
          },
        })),
        x_label: "Location",
        y1_label: "Samples to Test",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["7"],
    title: "Samples to Test Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
  SELECT
    COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
             CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
summary_results_unified
WHERE 1=1
and "SampleStatus" = 'I'
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
GROUP BY
    DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC
          `);
      return {
        id: "14",
        title: `Samples to Test Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Samples to Test",
      };
    },
  },

  // 8
  {
    collections: ["sample_overview_cards"],
    card_ids: ["8"],
    title: "Samples Tested Positive By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
          SELECT
            COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
            "location" as location_name,
            "GIS_ID"
            FROM summary_results_unified
            summary_results_unified
            WHERE 1=1
              and "finalresult_value" =  'Detected'
          ${
            start_date && end_date
              ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
              : ""
          }
         ${
           location
             ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
             : ""
         }
         ${analyte ? `AND "name" = '${analyte}'` : ""}
         ${group ? `AND "analyte_group" = '${group}'` : ""}
         ${category ? `AND "labName" = '${category}'` : ""}
         ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
        group by "location", GIS_ID
        order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
        limit 10
        `);

      return {
        id: "15",
        title: "Samples Tested Positive By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.GIS_ID,
          },
        })),
        x_label: "location",
        y1_label: "Samples Tested Positive",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["8"],
    title: "Samples Tested Positive Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
  SELECT
    COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
             CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
summary_results_unified
WHERE 1=1
and "finalresult_value" =  'Detected'
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
GROUP BY
    DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC
          `);
      return {
        id: "16",
        title: `Samples Tested Positive Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Samples Tested Positive",
      };
    },
  },

  // 9
  {
    collections: ["sample_overview_cards"],
    card_ids: ["9"],
    title: "Samples Tested Negative By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
          SELECT
            COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
            "location" as location_name,
            "GIS_ID"
            FROM summary_results_unified
            summary_results_unified
            WHERE 1=1
              and "finalresult_value" =  'Not Detected'
              and "ResultStatus" = 'A'
          ${
            start_date && end_date
              ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
              : ""
          }
         ${
           location
             ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
             : ""
         }
         ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
        group by "location", GIS_ID
        order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
        limit 10
        `);

      return {
        id: "17",
        title: "Samples Tested Negative By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.GIS_ID,
          },
        })),
        x_label: "Location",
        y1_label: "Samples Tested Negative",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["9"],
    title: "Samples Tested Negative Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
  SELECT
    COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
             CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
summary_results_unified
WHERE 1=1
and "finalresult_value" =  'Not Detected'
and "ResultStatus" = 'A'
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
GROUP BY
    DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC
          `);
      return {
        id: "18",
        title: `Samples Tested Negative Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Samples Tested Negative",
      };
    },
  },

  // 10
  {
    collections: ["sample_overview_cards"],
    card_ids: ["10"],
    title: "Samples Not Started By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
          SELECT
            COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
            "location" as location_name,
            "GIS_ID"
            FROM summary_results_unified
            WHERE 1=1
              and "SampleStatus" = 'U'
          ${
            start_date && end_date
              ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
              : ""
          }
         ${
           location
             ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
             : ""
         }
         ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
        group by "location", GIS_ID
        order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
        limit 10
        `);

      return {
        id: "19",
        title: "Samples Not Started By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.GIS_ID,
          },
        })),
        x_label: "Location",
        y1_label: "Samples Not Started",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["10"],
    title: "Samples Not Started Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
  SELECT
    COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
             CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
summary_results_unified
WHERE 1=1
and "SampleStatus" = 'U'
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
GROUP BY
    DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC
          `);
      return {
        id: "20",
        title: `Samples Not Started Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Samples Not Started",
      };
    },
  },

  //11
  {
    collections: ["sample_overview_cards"],
    card_ids: ["11"],
    title: "Samples Not Tested Collected By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
          SELECT
            COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
          GIS_ID as gis_id ,
          "location" as location_name,
            FROM summary_results_unified
            summary_results_unified
            WHERE 1=1
              and "TestStatus" = 'I'
          ${
            start_date && end_date
              ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
              : ""
          }
         ${
           location
             ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
             : ""
         }
         ${analyte ? `AND "name" = '${analyte}'` : ""}
         ${group ? `AND "analyte_group" = '${group}'` : ""}
         ${category ? `AND "labName" = '${category}'` : ""}
         ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
       
        group by GIS_ID, "location"
        order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
        limit 10
        `);

      return {
        id: "1",
        title: "Samples Not Tested Collected By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.gis_id,
          },
        })),
        x_label: "Locations",
        y1_label: "Number Of Not Tested Sample",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["11"],
    title: "Samples Not Tested  Collected Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
  SELECT
    COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
             CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
summary_results_unified
WHERE 1=1
  and "TestStatus" = 'I'
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

GROUP BY
    DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC
          `);
      return {
        id: "2",
        title: `Samples Not Tested Collected Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Number Of Not Tested Sample",
      };
    },
  },
  //12
  {
    collections: ["sample_overview_cards"],
    card_ids: ["12"],
    title: " Samples Authorized  Collected By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
          SELECT
            COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
          GIS_ID as gis_id ,
          "location" as location_name,

            FROM summary_results_unified
            summary_results_unified
            WHERE 1=1
            and "ResultStatus" = 'A'
            ${
              start_date && end_date
                ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
                : ""
            }
         ${
           location
             ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
             : ""
         }
         ${analyte ? `AND "name" = '${analyte}'` : ""}
         ${group ? `AND "analyte_group" = '${group}'` : ""}
         ${category ? `AND "labName" = '${category}'` : ""}
         ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

        group by GIS_ID,  "location"
        order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
        limit 10
        `);

      return {
        id: "1",
        title: "Samples Authorized Collected By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.gis_id,
          },
        })),
        x_label: "Locations",
        y1_label: "Number Of Authorized Sample",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["12"],
    title: "Samples Authorized  Collected Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
  SELECT
    COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
    DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
    CASE
        WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
        WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
             CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
        WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
        ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
    END AS formatted_period
FROM
summary_results_unified
WHERE 1=1
and "ResultStatus" = 'A'
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
    ${
      location
        ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

GROUP BY
    DATE_TRUNC('${dateTrunc}', "sampled_date")
ORDER BY
    time_period ASC
          `);
      return {
        id: "2",
        title: `Samples Authorized Collected Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Number Of Authorized Sample",
      };
    },
  },

  //13
  {
    collections: ["sample_overview_cards"],
    card_ids: ["13"],
    title: " Samples Not Authorized  Collected By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
            SELECT
              COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
            GIS_ID as gis_id ,
            "location" as location_name,
  
              FROM summary_results_unified
              summary_results_unified
              WHERE 1=1
              and "ResultStatus" <> 'A'
              ${
                start_date && end_date
                  ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
                  : ""
              }
           ${
             location
               ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
               : ""
           }
           ${analyte ? `AND "name" = '${analyte}'` : ""}
           ${group ? `AND "analyte_group" = '${group}'` : ""}
           ${category ? `AND "labName" = '${category}'` : ""}
           ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
  
          group by GIS_ID, "location"
          order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
          limit 10
          `);

      return {
        id: "1",
        title: "Samples Not Authorized Collected By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.gis_id,
          },
        })),
        x_label: "Locations",
        y1_label: "Number Of Not Authorized Sample",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["13"],
    title: "Samples Not Authorized  Collected Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
    SELECT
      COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
      DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
      CASE
          WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
          WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
          WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
               CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
          WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
          ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
      END AS formatted_period
  FROM
  summary_results_unified
  WHERE 1=1
  and "ResultStatus" <> 'A'
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
      ${
        location
          ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
          : ""
      }
      ${analyte ? `AND "name" = '${analyte}'` : ""}
      ${group ? `AND "analyte_group" = '${group}'` : ""}
      ${category ? `AND "labName" = '${category}'` : ""}
      ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

  GROUP BY
      DATE_TRUNC('${dateTrunc}', "sampled_date")
  ORDER BY
      time_period ASC
            `);
      return {
        id: "2",
        title: `Samples Not Authorized Collected Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Number Of Not Authorized Sample",
      };
    },
  },

  //14
  {
    collections: ["sample_overview_cards"],
    card_ids: ["14"],
    title: "Undetected Analytes  Collected By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
              SELECT
                COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
              GIS_ID as gis_id ,
              "location" as location_name,
    
                FROM summary_results_unified
                summary_results_unified
                WHERE 1=1
                and "finalresult_value" =  'Not Detected'
                and "ResultStatus" = 'A'
                  ${
                    start_date && end_date
                      ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
                      : ""
                  }
             ${
               location
                 ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
                 : ""
             }
            group by GIS_ID, "location"
            order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
            limit 10
            `);

      return {
        id: "1",
        title: "Undetected Analytes Collected By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.gis_id,
          },
        })),
        x_label: "Location",
        y1_label: "Number Of Undetected Analytes",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["14"],
    title: "Undetected Analytes  Collected Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
      SELECT
        COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
        DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
        CASE
            WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
            WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
            WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
                 CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
            WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
            ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        END AS formatted_period
    FROM
    summary_results_unified
    WHERE 1=1
    and "finalresult_value" =  'Not Detected'
    and "ResultStatus" = 'A'

    ${
      start_date && end_date
        ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
        : ""
    }
        ${
          location
            ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
            : ""
        }
        ${analyte ? `AND "name" = '${analyte}'` : ""}
        ${group ? `AND "analyte_group" = '${group}'` : ""}
        ${category ? `AND "labName" = '${category}'` : ""}
        ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
      

    GROUP BY
        DATE_TRUNC('${dateTrunc}', "sampled_date")
    ORDER BY
        time_period ASC
              `);
      return {
        id: "2",
        title: `Undetected Analytes Collected Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Number Of Undetected Analytes",
      };
    },
  },

  /**!SECTION
   *
   *
   * chem => raw_result => analyte.loq
   * raw >= loq ? detected : not detected
   *
   * if (raw less than loq)
   * then raw = loq * (90/100)
   *
   *
   */

  //16
  {
    collections: ["sample_overview_cards"],
    card_ids: ["16"],
    title: "Detected Analytes  Collected By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
              SELECT
                COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
              GIS_ID as gis_id ,
              "location" as location_name,
    
                FROM summary_results_unified
                summary_results_unified
                WHERE 1=1
                and "finalresult_value" =  'Detected'
                and "ResultStatus" = 'A'
                      ${
                        start_date && end_date
                          ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
                          : ""
                      }
             ${
               location
                 ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
                 : ""
             }
             ${analyte ? `AND "name" = '${analyte}'` : ""}
             ${group ? `AND "analyte_group" = '${group}'` : ""}
             ${category ? `AND "labName" = '${category}'` : ""}
             ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
           
    
            group by GIS_ID,    "location"
            order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
            limit 10
            `);

      return {
        id: "1",
        title: "Detected Analytes Collected By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.gis_id,
          },
        })),
        x_label: "Location",
        y1_label: "Number Of Detected Analytes",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["16"],
    title: "Detected Analytes  Collected Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
      SELECT
        COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
        DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
        CASE
            WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
            WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
            WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
                 CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
            WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
            ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
        END AS formatted_period
    FROM
    summary_results_unified
    WHERE 1=1
    and "finalresult_value" =  'Detected'
    and "ResultStatus" = 'A'

    ${
      start_date && end_date
        ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
        : ""
    }
        ${
          location
            ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
            : ""
        }
        ${analyte ? `AND "name" = '${analyte}'` : ""}
        ${group ? `AND "analyte_group" = '${group}'` : ""}
        ${category ? `AND "labName" = '${category}'` : ""}
        ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}

    GROUP BY
        DATE_TRUNC('${dateTrunc}', "sampled_date")
    ORDER BY
        time_period ASC
              `);
      return {
        id: "2",
        title: `Detected Analytes Collected Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Number Of Detected Analytes",
      };
    },
  },

  //20
  {
    collections: ["sample_overview_cards"],
    card_ids: ["20"],
    title: "Test Count for Authorized samples  Collected By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
                SELECT
                  COUNT(DISTINCT "Test_Number") as count , 
                GIS_ID as gis_id ,
                "location" as location_name,
      
                  FROM summary_results_unified
                  summary_results_unified
                  WHERE 1=1
                  and "ResultStatus" =  'A'
                  ${
                    start_date && end_date
                      ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
                      : ""
                  }
               ${
                 location
                   ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
                   : ""
               }
               ${analyte ? `AND "name" = '${analyte}'` : ""}
               ${group ? `AND "analyte_group" = '${group}'` : ""}
               ${category ? `AND "labName" = '${category}'` : ""}
               ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
      
              group by GIS_ID, "location"
              order by COUNT(DISTINCT "Test_Number") desc
              limit 10
              `);

      return {
        id: "1",
        title: "Test Count for Authorized samples Collected By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.gis_id,
          },
        })),
        x_label: "Locations",
        y1_label: "Test Count for Authorized samples",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["20"],
    title: "Test Count for Authorized samples  Collected Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
        SELECT
          COUNT(DISTINCT "Test_Number") AS count,
          DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
          CASE
              WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
              WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
              WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
                   CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
              WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
              ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
          END AS formatted_period
      FROM
      summary_results_unified
      WHERE 1=1
      and "ResultStatus" =  'A'

      ${
        start_date && end_date
          ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
          : ""
      }
          ${
            location
              ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
              : ""
          }
          ${analyte ? `AND "name" = '${analyte}'` : ""}
          ${group ? `AND "analyte_group" = '${group}'` : ""}
          ${category ? `AND "labName" = '${category}'` : ""}
          ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
 
      GROUP BY
          DATE_TRUNC('${dateTrunc}', "sampled_date")
      ORDER BY
          time_period ASC
                `);
      return {
        id: "2",
        title: `Test Count for Authorized samples Collected Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Test Count for Authorized samples",
      };
    },
  },

  //21
  {
    collections: ["sample_overview_cards"],
    card_ids: ["21"],
    title: "Rejected Samples  Collected By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
                  SELECT
                    COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
                  GIS_ID as gis_id ,
                  "location" as location_name,
        
                    FROM summary_results_unified
                    summary_results_unified
                    WHERE 1=1
                    and "SampleStatus" =  'R'
                              ${
                                start_date && end_date
                                  ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
                                  : ""
                              }
                 ${
                   location
                     ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
                     : ""
                 }
                 ${analyte ? `AND "name" = '${analyte}'` : ""}
                 ${group ? `AND "analyte_group" = '${group}'` : ""}
                 ${category ? `AND "labName" = '${category}'` : ""}
                 ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
        
                group by GIS_ID, "location"
                order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
                limit 10
                `);

      return {
        id: "1",
        title: "Rejected Samples Collected By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.gis_id,
          },
        })),
        x_label: "Locations",
        y1_label: "Number Rejected Samples",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["21"],
    title: " Rejected Samples  Collected Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
          SELECT
            COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
            DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
            CASE
                WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
                WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
                WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
                     CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
                WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
                ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
            END AS formatted_period
        FROM
        summary_results_unified
        WHERE 1=1
        and "SampleStatus" =  'R'
  
        ${
          start_date && end_date
            ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
            : ""
        }
            ${
              location
                ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
                : ""
            }
            ${analyte ? `AND "name" = '${analyte}'` : ""}
            ${group ? `AND "analyte_group" = '${group}'` : ""}
            ${category ? `AND "labName" = '${category}'` : ""}
            ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
   
        GROUP BY
            DATE_TRUNC('${dateTrunc}', "sampled_date")
        ORDER BY
            time_period ASC
                  `);
      return {
        id: "2",
        title: `Rejected Samples Collected Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Locations",
        y1_label: "Number Rejected Samples",
      };
    },
  },

  //22
  {
    collections: ["sample_overview_cards"],
    card_ids: ["22"],
    title: "Cancelled Samples  Collected By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
                  SELECT
                    COUNT(DISTINCT "SAMPLE_NUMBER") as count , 
                  GIS_ID as gis_id ,
                  "location" as location_name,
        
                    FROM summary_results_unified
                    summary_results_unified
                    WHERE 1=1
                    and "SampleStatus" =  'X'
                    ${
                      start_date && end_date
                        ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
                        : ""
                    }
                 ${
                   location
                     ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
                     : ""
                 }
                 ${analyte ? `AND "name" = '${analyte}'` : ""}
                 ${group ? `AND "analyte_group" = '${group}'` : ""}
                 ${category ? `AND "labName" = '${category}'` : ""}
                 ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
        
                group by GIS_ID, "location"
                order by COUNT(DISTINCT "SAMPLE_NUMBER") desc
                limit 10
                `);

      return {
        id: "1",
        title: "Cancelled Samples Collected By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
          metaData: {
            gis_id: item.gis_id,
          },
        })),
        x_label: "Locations",
        y1_label: "Number Cancelled Samples",
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["22"],
    title: "Cancelled Samples  Collected Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
          SELECT
            COUNT(DISTINCT "SAMPLE_NUMBER") AS count,
            DATE_TRUNC('${dateTrunc}', "sampled_date") AS time_period,
            CASE
                WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-W%V')
                WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
                WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y') || '-Q' ||
                     CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', "sampled_date")) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
                WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y')
                ELSE strftime(DATE_TRUNC('${dateTrunc}', "sampled_date"), '%Y-%m')
            END AS formatted_period
        FROM
        summary_results_unified
        WHERE 1=1
        and "SampleStatus" =  'X'
  
        ${
          start_date && end_date
            ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
            : ""
        }
            ${
              location
                ? `AND ("CatchmentArea" = '${location}' OR "GIS_ID" = '${location}')`
                : ""
            }
            ${analyte ? `AND "name" = '${analyte}'` : ""}
            ${group ? `AND "analyte_group" = '${group}'` : ""}
            ${category ? `AND "labName" = '${category}'` : ""}
            ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
          
   
        GROUP BY
            DATE_TRUNC('${dateTrunc}', "sampled_date")
        ORDER BY
            time_period ASC
                  `);
      return {
        id: "2",
        title: `Cancelled Samples Collected Over Time (${date_frame})`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Time",
        y1_label: "Number Cancelled Samples",
      };
    },
  },
  // analyte_insights_cards
  {
    collections: ["analyte_insights_cards"],
    card_ids: ["1", "3", "4"],
    title: "Sample Presence By Location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
    WITH base AS (
  SELECT
    s.*,
    l.loq_value
  FROM summary_results_unified s
  LEFT JOIN analyte_loq_clean l
    ON s.name = l.Analyte
  WHERE "ResultStatus" = 'A'
    ${
      location
        ? `AND ( "CatchmentArea" = '${location}' OR "GIS_ID" = '${location}' )`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
),
dedup AS (
  -- Keep ONE test per analyte per sample: the latest by DATE_COMPLETED
  SELECT
    b.*,
    ROW_NUMBER() OVER (
      PARTITION BY b."SAMPLE_NUMBER", b."name"
      ORDER BY b."DATE_COMPLETED" DESC
    ) AS rn
  FROM base b
),
latest AS (
  SELECT * FROM dedup WHERE rn = 1
),
tagged AS (
  -- Compute detection on the kept row
  SELECT
    l.*,
    CASE
      WHEN l."ANALYSIS" = 'RADIOACTIVITY' THEN
        (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
      WHEN TRY_CAST(l."rawResult" AS DOUBLE) IS NOT NULL AND l.loq_value IS NOT NULL THEN
        CASE
          WHEN CAST(l."rawResult" AS DOUBLE) > l.loq_value THEN TRUE
          ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
        END
      ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
    END AS is_detected
  FROM latest l
),
classified AS (
  SELECT
    "SAMPLE_NUMBER",
    sampled_date,
    "location",
    "CatchmentArea",
    "GIS_ID",
    "name",
    "analyte_group",
    "labName",
    "sample_type",
    CASE WHEN is_detected THEN 'Detected' ELSE 'Not Detected' END AS finalresult_value
  FROM tagged
)
SELECT
  COUNT(*) FILTER (
    WHERE finalresult_value = 'Detected'
      ${
        start_date && end_date
          ? `AND sampled_date BETWEEN '${start_date}' AND '${end_date}'`
          : ""
      }
  ) AS detected_samples, 

  COUNT(*) FILTER (
    WHERE finalresult_value = 'Not Detected'
      ${
        start_date && end_date
          ? `AND sampled_date BETWEEN '${start_date}' AND '${end_date}'`
          : ""
      }
  ) AS not_detected_samples,

  "location" AS location_name,
  "CatchmentArea" AS catchment_name

FROM classified
WHERE 1=1
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
GROUP BY "location", "CatchmentArea"
ORDER BY
  COUNT(DISTINCT "SAMPLE_NUMBER") FILTER (
    WHERE 1=1
    ${
      start_date && end_date
        ? `AND sampled_date BETWEEN '${start_date}' AND '${end_date}'`
        : ""
    }
  ) DESC
LIMIT 10;

    `);

      return {
        id: "1",
        title: "Sample Presence By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y1: item.detected_samples,
          y2: item.not_detected_samples,
          metaData: {
            catchment_name: item.catchment_name,
          },
        })),
        x_label: "Locations",
        y1_label: "Number Of Detected Sample",
        y2_label: "Number Of Non Detected Sample",
      };
    },
  },
  {
    collections: ["analyte_insights_cards"],
    card_ids: ["1", "3", "4"],
    title: "Detection Trend Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
     WITH base AS (
  SELECT
    s.*,
    l.loq_value
  FROM summary_results_unified s
  LEFT JOIN analyte_loq_clean l
    ON s.name = l.Analyte
  WHERE "ResultStatus" = 'A'
    ${
      location
        ? `AND ( "CatchmentArea" = '${location}' OR "GIS_ID" = '${location}' )`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
),
dedup AS (
  -- Keep ONE test per analyte per sample: the latest by DATE_COMPLETED
  SELECT
    b.*,
    ROW_NUMBER() OVER (
      PARTITION BY b."SAMPLE_NUMBER", b."name"
      ORDER BY b."DATE_COMPLETED" DESC
    ) AS rn
  FROM base b
),
latest AS (
  SELECT * FROM dedup WHERE rn = 1
),
tagged AS (
  -- Compute detection on the kept row
  SELECT
    l.*,
    CASE
      WHEN l."ANALYSIS" = 'RADIOACTIVITY' THEN
        (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
      WHEN TRY_CAST(l."rawResult" AS DOUBLE) IS NOT NULL AND l.loq_value IS NOT NULL THEN
        CASE
          WHEN CAST(l."rawResult" AS DOUBLE) > l.loq_value THEN TRUE
          ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
        END
      ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
    END AS is_detected
  FROM latest l
),
classified AS (
  SELECT
    "SAMPLE_NUMBER",
    sampled_date,
    "CatchmentArea",
    "GIS_ID",
    "name",
    "analyte_group",
    "labName",
    "sample_type",
    CASE WHEN is_detected THEN 'Detected' ELSE 'Not Detected' END AS finalresult_value
  FROM tagged
)
SELECT
  COUNT(*) FILTER (
    WHERE finalresult_value = 'Detected'
      ${
        start_date && end_date
          ? `AND sampled_date BETWEEN '${start_date}' AND '${end_date}'`
          : ""
      }
  ) AS detected_count,

  COUNT(*) FILTER (
    WHERE finalresult_value = 'Not Detected'
      ${
        start_date && end_date
          ? `AND sampled_date BETWEEN '${start_date}' AND '${end_date}'`
          : ""
      }
  ) AS not_detected_count,

  DATE_TRUNC('${dateTrunc}', sampled_date) AS time_period,
  CASE
      WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y-W%V')
      WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y-%m')
      WHEN '${dateTrunc}' = 'quarter' THEN
           strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y') || '-Q' ||
           CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', sampled_date)) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
      WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y')
      ELSE strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y-%m')
  END AS formatted_period
FROM classified
WHERE 1=1
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
GROUP BY
  DATE_TRUNC('${dateTrunc}', sampled_date)
ORDER BY
  time_period ASC;

    `);

      return {
        id: "2",
        title: `Detection Trend Over Time (${date_frame})`,
        type: "multi-line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y1: parseInt(item.detected_count),
          y2: parseInt(item.not_detected_count),
        })),
        x_label: "Period",
        y1_label: "Number Of Detected Sample",
        y2_label: "Number Of Non Detected Sample",
      };
    },
  },
  {
    collections: ["analyte_insights_cards"],
    card_ids: ["1", "3", "4"],
    title: "top 10 detected analytes",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
     WITH base AS (
  SELECT
    s.*,
    l.loq_value
  FROM summary_results_unified s
  LEFT JOIN analyte_loq_clean l
    ON s.name = l.Analyte
  WHERE "ResultStatus" = 'A'
    ${
      location
        ? `AND ( "CatchmentArea" = '${location}' OR "GIS_ID" = '${location}' )`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
),
dedup AS (
  -- Keep ONE test per analyte per sample: the latest by DATE_COMPLETED
  SELECT
    b.*,
    ROW_NUMBER() OVER (
      PARTITION BY b."SAMPLE_NUMBER", b."name"
      ORDER BY b."DATE_COMPLETED" DESC
    ) AS rn
  FROM base b
),
latest AS (
  SELECT * FROM dedup WHERE rn = 1
),
tagged AS (
  -- Compute detection on the kept row
  SELECT
    l.*,
    CASE
      WHEN l."ANALYSIS" = 'RADIOACTIVITY' THEN
        (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
      WHEN TRY_CAST(l."rawResult" AS DOUBLE) IS NOT NULL AND l.loq_value IS NOT NULL THEN
        CASE
          WHEN CAST(l."rawResult" AS DOUBLE) > l.loq_value THEN TRUE
          ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
        END
      ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
    END AS is_detected
  FROM latest l
),
classified AS (
  SELECT
    "SAMPLE_NUMBER",
    sampled_date,
    "CatchmentArea",
    "GIS_ID",
    "name",
    "analyte_group",
    "labName",
    "sample_type",
    CASE WHEN is_detected THEN 'Detected' ELSE 'Not Detected' END AS finalresult_value
  FROM tagged
)
SELECT
  COUNT(*) AS count,
  "name" AS analyte_name
FROM classified
WHERE finalresult_value = 'Detected'
${
  start_date && end_date
    ? `AND (sampled_date BETWEEN '${start_date}' AND '${end_date}')`
    : ""
}
GROUP BY "name"
ORDER BY
  COUNT(*) DESC
LIMIT 10;

    `);

      return {
        id: "3",
        title: "Top 10 Detected Analytes",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.analyte_name,
          y1: item.count,
        })),
        x_label: "Analyte Name",
        y1_label: "Number Of Detections",
      };
    },
  },
  {
    collections: ["analyte_insights_cards"],
    card_ids: ["2"],
    title: "Presence rate by analyte",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const res = await queryDuck(`
      WITH base AS (
  SELECT s.*, l.loq_value
  FROM summary_results_unified s
  LEFT JOIN analyte_loq_clean l ON s.name = l.Analyte
  WHERE "ResultStatus" = 'A'
    ${
      location
        ? `AND ( "CatchmentArea" = '${location}' OR "GIS_ID" = '${location}' )`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
),
dedup AS (
  SELECT b.*,
         ROW_NUMBER() OVER (
           PARTITION BY b."SAMPLE_NUMBER", b."name"
           ORDER BY b."DATE_COMPLETED" DESC
         ) AS rn
  FROM base b
),
latest AS (SELECT * FROM dedup WHERE rn = 1),
tagged AS (
  SELECT l.*,
         CASE
           WHEN l."ANALYSIS" = 'RADIOACTIVITY' THEN (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
           WHEN TRY_CAST(l."rawResult" AS DOUBLE) IS NOT NULL AND l.loq_value IS NOT NULL
             THEN CASE WHEN CAST(l."rawResult" AS DOUBLE) > l.loq_value THEN TRUE
                       ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED')) END
           ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
         END AS is_detected
  FROM latest l
),
classified AS (
  SELECT "SAMPLE_NUMBER", sampled_date, "name",
         CASE WHEN is_detected THEN 'Detected' ELSE 'Not Detected' END AS finalresult_value
  FROM tagged
)
SELECT
  "name" AS analyte_name,
  ROUND(100.0 * COUNT(*) FILTER (WHERE finalresult_value = 'Detected') / NULLIF(COUNT(*), 0), 2) AS presence_rate_pct
FROM classified
WHERE 1=1
  ${
    start_date && end_date
      ? `AND sampled_date BETWEEN '${start_date}' AND '${end_date}'`
      : ""
  }
GROUP BY "name"
ORDER BY presence_rate_pct DESC
LIMIT 10;

    `);

      return {
        id: "4",
        title: "Presence rate by analyte",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.analyte_name,
          y1: item.presence_rate_pct,
        })),
        x_label: "Analyte",
        y1_label: "Presence Rate (%)",
      };
    },
  },
  {
    collections: ["analyte_insights_cards"],
    card_ids: ["2"],
    title: "Presence rate over time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        date_frame = "week",
        location,
        group,
        category,
        analyte,
        sample_type,
      } = params;
      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
     WITH base AS (
  SELECT s.*, l.loq_value
  FROM summary_results_unified s
  LEFT JOIN analyte_loq_clean l ON s.name = l.Analyte
  WHERE "ResultStatus" = 'A'
    ${
      location
        ? `AND ( "CatchmentArea" = '${location}' OR "GIS_ID" = '${location}' )`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
),
dedup AS (
  SELECT b.*,
         ROW_NUMBER() OVER (
           PARTITION BY b."SAMPLE_NUMBER", b."name"
           ORDER BY b."DATE_COMPLETED" DESC
         ) AS rn
  FROM base b
),
latest AS (SELECT * FROM dedup WHERE rn = 1),
tagged AS (
  SELECT l.*,
         CASE
           WHEN l."ANALYSIS" = 'RADIOACTIVITY' THEN (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
           WHEN TRY_CAST(l."rawResult" AS DOUBLE) IS NOT NULL AND l.loq_value IS NOT NULL
             THEN CASE WHEN CAST(l."rawResult" AS DOUBLE) > l.loq_value THEN TRUE
                       ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED')) END
           ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
         END AS is_detected
  FROM latest l
),
classified AS (
  SELECT sampled_date,
         CASE WHEN is_detected THEN 'Detected' ELSE 'Not Detected' END AS finalresult_value
  FROM tagged
)
SELECT
  ROUND(100.0 * COUNT(*) FILTER (WHERE finalresult_value = 'Detected') / NULLIF(COUNT(*),0), 2) AS detected_pct,
  ROUND(100.0 * COUNT(*) FILTER (WHERE finalresult_value = 'Not Detected') / NULLIF(COUNT(*),0), 2) AS not_detected_pct,
  DATE_TRUNC('${dateTrunc}', sampled_date) AS time_period,
  CASE
    WHEN '${dateTrunc}' = 'week' THEN strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y-W%V')
    WHEN '${dateTrunc}' = 'month' THEN strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y-%m')
    WHEN '${dateTrunc}' = 'quarter' THEN strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y') || '-Q' ||
         CAST(((CAST(strftime('%m', DATE_TRUNC('${dateTrunc}', sampled_date)) AS INTEGER) - 1) / 3 + 1) AS VARCHAR)
    WHEN '${dateTrunc}' = 'year' THEN strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y')
    ELSE strftime(DATE_TRUNC('${dateTrunc}', sampled_date), '%Y-%m')
  END AS formatted_period
FROM classified
WHERE 1=1
  ${
    start_date && end_date
      ? `AND sampled_date BETWEEN '${start_date}' AND '${end_date}'`
      : ""
  }
GROUP BY DATE_TRUNC('${dateTrunc}', sampled_date)
ORDER BY time_period ASC;

    `);
      return {
        id: "5",
        title: `Presence rate over time (${date_frame})`,
        type: "multi-line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y1: parseFloat(item.detected_pct),
          // y2: parseFloat(item.not_detected_pct),
        })),
        x_label: "Period",
        y1_label: "Detected Rate (%)",
        // y2_label: "Not Detected Rate (%)",
      };
    },
  },
  {
    collections: ["analyte_insights_cards"],
    card_ids: ["2"],
    title: "presense rate by location",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await queryDuck(`
    WITH base AS (
  SELECT s.*, l.loq_value
  FROM summary_results_unified s
  LEFT JOIN analyte_loq_clean l ON s.name = l.Analyte
  WHERE "ResultStatus" = 'A'
    ${
      location
        ? `AND ( "CatchmentArea" = '${location}' OR "GIS_ID" = '${location}' )`
        : ""
    }
    ${analyte ? `AND "name" = '${analyte}'` : ""}
    ${group ? `AND "analyte_group" = '${group}'` : ""}
    ${category ? `AND "labName" = '${category}'` : ""}
    ${sample_type ? `AND "sample_type" = '${sample_type}'` : ""}
),
dedup AS (
  SELECT b.*,
         ROW_NUMBER() OVER (
           PARTITION BY b."SAMPLE_NUMBER", b."name"
           ORDER BY b."DATE_COMPLETED" DESC
         ) AS rn
  FROM base b
),
latest AS (SELECT * FROM dedup WHERE rn = 1),
tagged AS (
  SELECT l.*,
         CASE
           WHEN l."ANALYSIS" = 'RADIOACTIVITY' THEN (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
           WHEN TRY_CAST(l."rawResult" AS DOUBLE) IS NOT NULL AND l.loq_value IS NOT NULL
             THEN CASE WHEN CAST(l."rawResult" AS DOUBLE) > l.loq_value THEN TRUE
                       ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED')) END
           ELSE (UPPER(l."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
         END AS is_detected
  FROM latest l
),
classified AS (
  SELECT "CatchmentArea" AS location,
         sampled_date,
         CASE WHEN is_detected THEN 'Detected' ELSE 'Not Detected' END AS finalresult_value
  FROM tagged
)
SELECT
  ROUND(100.0 * COUNT(*) FILTER (WHERE finalresult_value = 'Detected') / NULLIF(COUNT(*), 0), 2) AS presence_rate_pct,
  location
FROM classified
WHERE 1=1
  ${
    start_date && end_date
      ? `AND sampled_date BETWEEN '${start_date}' AND '${end_date}'`
      : ""
  }
GROUP BY location
ORDER BY
  COUNT(*) FILTER (WHERE finalresult_value = 'Detected') DESC
LIMIT 10;

    `);

      return {
        id: "6",
        title: "presense rate by location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location,
          y1: item.presence_rate_pct,
        })),
        x_label: "Location",
        y1_label: "Number Of Detections",
      };
    },
  },

  // risk_trends_cards
  {
    collections: ["risk_trends_cards"],
    card_ids: ["1"],
    level: ["*"],
    title: "Top 10 Risk Analytes",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
        risks,
        persona,
        historical,
      } = params;

      const risk_score_id = risks ? risks.split(",") : [];

      const personaList = persona
        ? persona
            .split(",")
            ?.filter(Boolean)
            ?.map((p: string) => p.trim())
            ?.filter(Boolean)
        : [];

      const res = await queryDuck(`
      SELECT
       (SUM(CAST(rs.last_risk_category_id AS DOUBLE)) / count(*)) AS avg,
      -- (count(*) * sum(risk_count)) as count, 
       count(*)  as count, 
        da.name AS analyte_name
      FROM risk_score_info_summaries rs
      LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
      WHERE 1=1
        AND rs.lastGroup = true
        AND rs.risk_count > 1  ${
          historical ? "" : " and first_risk_date >= '2025-05-01' "
        } 
        ${
          start_date && end_date
            ? `
              AND (
                CAST(rs.first_risk_date AS DATE)
 BETWEEN '${start_date}' AND '${end_date}'
                OR
                CAST(rs.last_risk_date AS DATE)
 BETWEEN '${start_date}' AND '${end_date}'
              )
            `
            : ""
        }
        ${location ? `AND rs.GIS_ID = '${location}'` : ""}
        ${analyte ? `AND da.name = '${analyte}'` : ""}
        ${category ? `AND da.labName = '${category}'` : ""}
        ${group ? `AND da.Analyte_Group = '${group}'` : ""}
        ${
          personaList && personaList.length
            ? `AND da.persona IN (${personaList
                .map((p: string) => `'${p.replace(/'/g, "''")}'`)
                .join(",")})`
            : ""
        }
        ${
          risk_score_id && risk_score_id.length
            ? `AND rs.last_risk_category_id IN (${risk_score_id
                .map((r: string) => Number(r))
                .join(",")})`
            : ""
        }
      GROUP BY da.name
      ORDER BY (SUM(CAST(rs.last_risk_category_id AS DOUBLE)) / count(*)) DESC , count(*) desc
      LIMIT 10
    `);

      return {
        id: "1",
        title: `Top 10 highest Risk Analytes`,
        type: "bar",
        data: res.map((item: any) => ({
          x: item.analyte_name,
          y1: item.avg,
        })),
        y_labels: res.map((r) => `risks count: ${r.count}`),
        x_label: "Analyte Name",
        y1_label: "Average Risk",
        y_value_label: "Average Risk Score",
      };
    },
  },
  {
    collections: ["risk_trends_cards"],
    card_ids: ["1"],
    level: ["*"],
    title: "Risk Sample Collected Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
        risks,
        persona,
        historical,
      } = params;

      const risk_score_id = risks ? risks.split(",") : [];

      const personaList = persona
        ? persona
            .split(",")
            ?.filter(Boolean)
            ?.map((p: string) => p.trim())
            ?.filter(Boolean)
        : [];

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await queryDuck(`
      SELECT
        COUNT(*) AS count,
        DATE_TRUNC('${dateTrunc}', COALESCE(rs.last_risk_date::timestamp, rs.first_risk_date::timestamp)) AS time_period,
          CASE
    WHEN '${dateTrunc}' = 'quarter' THEN 
      CONCAT(
        CAST(EXTRACT(YEAR FROM time_period) AS VARCHAR),
        '-Q',
        CAST(EXTRACT(QUARTER FROM time_period) AS VARCHAR)
      )
    WHEN '${dateTrunc}' = 'week' THEN 
      strftime(time_period, '%G-W%V')
    WHEN '${dateTrunc}' = 'month' THEN 
      strftime(time_period, '%Y-%m')
    WHEN '${dateTrunc}' = 'year' THEN 
      strftime(time_period, '%Y')
    ELSE 
      strftime(time_period, '%Y-%m')
  END AS formatted_period,
      FROM risk_score_info_summaries rs
      LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
      WHERE 1=1
        AND rs.lastGroup = true
        AND rs.risk_count > 1  
        ${historical ? "" : " and first_risk_date >= '2025-05-01' "} 
        ${
          start_date && end_date
            ? `
              AND (
                rs.first_risk_date BETWEEN '${start_date}' AND '${end_date}'
                OR
                rs.last_risk_date BETWEEN '${start_date}' AND '${end_date}'
              )
            `
            : ""
        }
        ${location ? `AND rs.GIS_ID = '${location}'` : ""}
        ${analyte ? `AND da.name = '${analyte}'` : ""}
        ${category ? `AND da.labName = '${category}'` : ""}
        ${group ? `AND da.Analyte_Group = '${group}'` : ""}
        ${
          risk_score_id && risk_score_id.length
            ? `AND rs.last_risk_category_id IN (${risk_score_id
                .map((r: string) => Number(r))
                .join(",")})`
            : ""
        }
        ${
          personaList && personaList.length
            ? `AND da.persona IN (${personaList
                .map((p: string) => `'${p.replace(/'/g, "''")}'`)
                .join(",")})`
            : ""
        }
      GROUP BY DATE_TRUNC('${dateTrunc}', COALESCE(rs.last_risk_date::timestamp, rs.first_risk_date::timestamp))
      ORDER BY time_period ASC
    `);

      return {
        id: "2",
        title: `Risks Count By ${date_frame}`,
        type: "line",
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
        x_label: "Date",
        y1_label: "Count Of Risk Detected Sample",
      };
    },
  },
  {
    collections: ["risk_trends_cards"],
    card_ids: ["1", "4"],
    level: ["*"],
    title: "Top 10 Locations",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
        risks,
        persona,
        historical,
      } = params;

      const risk_score_id = risks ? risks.split(",") : [];

      const personaList = persona
        ? persona
            .split(",")
            ?.filter(Boolean)
            ?.map((p: string) => p.trim())
            ?.filter(Boolean)
        : [];

      const res = await queryDuck(`
      SELECT
        rs.GIS_ID,
        (SUM(CAST(rs.last_risk_category_id AS DOUBLE)) / count(*)) AS avg,
        COUNT(*) AS count
      FROM risk_score_info_summaries rs
      LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
      WHERE 1=1
        AND rs.lastGroup = true
        AND rs.risk_count > 1  ${
          historical ? "" : " and first_risk_date >= '2025-05-01' "
        } 
        ${
          start_date && end_date
            ? `
              AND (
                CAST(rs.first_risk_date AS DATE) BETWEEN '${start_date}' AND '${end_date}'
                OR
                CAST(rs.last_risk_date  AS DATE) BETWEEN '${start_date}' AND '${end_date}'
              )
            `
            : ""
        }
        ${location ? `AND GIS_ID = '${location}'` : ""}
        ${analyte ? `AND da.name = '${analyte}'` : ""}
        ${category ? `AND da.labName = '${category}'` : ""}
        ${group ? `AND da.Analyte_Group = '${group}'` : ""}
        ${
          personaList && personaList.length
            ? `AND da.persona IN (${personaList
                .map((p: string) => `'${p.replace(/'/g, "''")}'`)
                .join(",")})`
            : ""
        }
        ${
          risk_score_id && risk_score_id.length
            ? `AND rs.last_risk_category_id IN (${risk_score_id
                .map((r: string) => Number(r))
                .join(",")})`
            : ""
        }
      GROUP BY rs.GIS_ID
      ORDER BY (SUM(CAST(rs.last_risk_category_id AS DOUBLE)) / count(*)) DESC, COUNT(*) DESC
      LIMIT 10
    `);

      return {
        id: "3",
        title: `Top 10 highest Risk Locations`,
        type: "bar",
        data: res.map((item: any) => ({
          x: item.GIS_ID,
          y1: item.avg,
        })),
        // Optional helper labels (same pattern you used for analytes)
        y_labels: res.map((r: any) => `count of risks: ${r.count}`),
        x_label: "Location",
        y1_label: "Risks Mean",
      };
    },
  },
  // 2nd
  {
    collections: ["risk_trends_cards"],
    card_ids: ["2"],
    level: ["*"],
    title: "Top 10 Emerging Risks",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
        risks,
        persona,
        historical,
      } = params;

      const risk_score_id = risks ? risks.split(",") : [];

      const personaList = persona
        ? persona
            .split(",")
            ?.filter(Boolean)
            ?.map((p: string) => p.trim())
            ?.filter(Boolean)
        : [];

      const res = await queryDuck(`
      SELECT
       (SUM(CAST(rs.last_risk_category_id AS DOUBLE)) / count(*)) AS avg,
      -- (count(*) * sum(risk_count)) as count, 
       count(*)  as count, 
        da.name AS analyte_name
      FROM risk_score_info_summaries rs
      LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
      WHERE 1=1
        AND rs.lastGroup = true
        AND rs.risk_count > 1  ${
          historical ? "" : " and first_risk_date >= '2025-05-01' "
        } 
        and emerging_risk_flag = true
        ${
          start_date && end_date
            ? `
              AND (
                CAST(rs.first_risk_date AS DATE)
 BETWEEN '${start_date}' AND '${end_date}'
                OR
                CAST(rs.last_risk_date AS DATE)
 BETWEEN '${start_date}' AND '${end_date}'
              )
            `
            : ""
        }
        ${location ? `AND rs.GIS_ID = '${location}'` : ""}
        ${analyte ? `AND da.name = '${analyte}'` : ""}
        ${category ? `AND da.labName = '${category}'` : ""}
        ${group ? `AND da.Analyte_Group = '${group}'` : ""}
        ${
          personaList && personaList.length
            ? `AND da.persona IN (${personaList
                .map((p: string) => `'${p.replace(/'/g, "''")}'`)
                .join(",")})`
            : ""
        }
        ${
          risk_score_id && risk_score_id.length
            ? `AND rs.last_risk_category_id IN (${risk_score_id
                .map((r: string) => Number(r))
                .join(",")})`
            : ""
        }
      GROUP BY da.name
      ORDER BY (SUM(CAST(rs.last_risk_category_id AS DOUBLE)) / count(*)) DESC , count(*) desc
      LIMIT 10
    `);

      return {
        id: "1",
        title: `Top 10 Emerging Risks`,
        type: "bar",
        data: res.map((item: any) => ({
          x: item.analyte_name,
          y1: item.avg,
        })),
        y_labels: res.map((r) => `count of risks: ${r.count}`),
        x_label: "Analyte Name",
        y1_label: "Number Of Detected Sample",
      };
    },
  },
  {
    collections: ["risk_trends_cards"],
    card_ids: ["2"],
    level: ["*"],
    title: "Top 10 Locations with Emerging Threats",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        date_frame = "week",
        analyte,
        category,
        group,
        sample_type,
        location,
        risks,
        persona,
        historical,
      } = params;

      const risk_score_id = risks ? risks.split(",") : [];

      const personaList = persona
        ? persona
            .split(",")
            ?.filter(Boolean)
            ?.map((p: string) => p.trim())
            ?.filter(Boolean)
        : [];

      const res = await queryDuck(`
      SELECT
        rs.GIS_ID,
        (SUM(CAST(rs.last_risk_category_id AS DOUBLE)) / count(*)) AS avg,
        COUNT(*) AS count
      FROM risk_score_info_summaries rs
      LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
      WHERE 1=1
        AND rs.lastGroup = true
        AND rs.risk_count > 1  ${
          historical ? "" : " and first_risk_date >= '2025-05-01' "
        } 
        and emerging_risk_flag = true
        ${
          start_date && end_date
            ? `
              AND (
                CAST(rs.first_risk_date AS DATE) BETWEEN '${start_date}' AND '${end_date}'
                OR
                CAST(rs.last_risk_date  AS DATE) BETWEEN '${start_date}' AND '${end_date}'
              )
            `
            : ""
        }
        ${location ? `AND rs.GIS_ID = '${location}'` : ""}
        ${analyte ? `AND da.name = '${analyte}'` : ""}
        ${category ? `AND da.labName = '${category}'` : ""}
        ${group ? `AND da.Analyte_Group = '${group}'` : ""}
        ${
          personaList && personaList.length
            ? `AND da.persona IN (${personaList
                .map((p: string) => `'${p.replace(/'/g, "''")}'`)
                .join(",")})`
            : ""
        }
        ${
          risk_score_id && risk_score_id.length
            ? `AND rs.last_risk_category_id IN (${risk_score_id
                .map((r: string) => Number(r))
                .join(",")})`
            : ""
        }
      GROUP BY rs.GIS_ID
      ORDER BY (SUM(CAST(rs.last_risk_category_id AS DOUBLE)) / count(*)) DESC, COUNT(*) DESC
      LIMIT 10
    `);

      return {
        id: "3",
        title: `Top 10 Locations with Emerging Threats`,
        type: "bar",
        data: res.map((item: any) => ({
          x: item.GIS_ID,
          y1: item.avg,
        })),
        // Optional helper labels (same pattern you used for analytes)
        y_labels: res.map((r: any) => `count of risks: ${r.count}`),
        x_label: "Location",
        y1_label: "Count Of Risk Detected Sample",
      };
    },
  },
  // card 3
  // card 3 — analytes (mirrors card 2 analytes)
  {
    collections: ["risk_trends_cards"],
    card_ids: ["3"],
    level: ["*"],
    title: "Top 10 Radioactivity Risks Analytes",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        analyte,
        category,
        group,
        sample_type,
        location,
        risks,
        persona,
        historical,
      } = params;

      const risk_score_id = risks ? risks.split(",") : [];
      const personaList = persona
        ? persona
            .split(",")
            .filter(Boolean)
            .map((p: string) => p.trim())
            .filter(Boolean)
        : [];

      const res = await queryDuck(`
      SELECT
        (SUM(CAST(rs.last_risk_category_id AS DOUBLE)) / COUNT(*)) AS avg,
        COUNT(*) AS count,
        da.name AS analyte_name
      FROM risk_score_info_summaries rs
      LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
      WHERE 1=1
        AND rs.lastGroup = true
        AND rs.risk_count > 1  ${
          historical ? "" : " and first_risk_date >= '2025-05-01' "
        } 
        AND da.labName = 'Radioactivity'
        ${
          start_date && end_date
            ? `
              AND (
                CAST(rs.first_risk_date AS DATE) BETWEEN '${start_date}' AND '${end_date}'
                OR
                CAST(rs.last_risk_date  AS DATE) BETWEEN '${start_date}' AND '${end_date}'
              )
            `
            : ""
        }
        ${location ? `AND rs.GIS_ID = '${location}'` : ""}
        ${analyte ? `AND da.name = '${analyte}'` : ""}
        ${category ? `AND da.labName = '${category}'` : ""}
        ${group ? `AND da.Analyte_Group = '${group}'` : ""}
        ${
          personaList.length
            ? `AND da.persona IN (${personaList
                .map((p: string) => `'${p.replace(/'/g, "''")}'`)
                .join(",")})`
            : ""
        }
        ${
          risk_score_id.length
            ? `AND rs.last_risk_category_id IN (${risk_score_id
                .map((r: string) => Number(r))
                .join(",")})`
            : ""
        }
      GROUP BY da.name
      ORDER BY (SUM(CAST(rs.last_risk_category_id AS DOUBLE)) / COUNT(*)) DESC, COUNT(*) DESC
      LIMIT 10
    `);

      return {
        id: "1",
        title: `Count of top 10 Radioactivity Risks Analytes`,
        type: "bar",
        data: res.map((item: any) => ({
          x: item.analyte_name,
          y1: item.avg,
        })),
        y_labels: res.map((r: any) => `count of risks: ${r.count}`),
        x_label: "Analyte Name",
        y1_label: "Count Of Detected Sample",
      };
    },
  },
  // card 3 — locations (mirrors card 2 locations)
  {
    collections: ["risk_trends_cards"],
    card_ids: ["3"],
    level: ["*"],
    title: "Top 10 Locations with Radioactivity Risks",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        analyte,
        category,
        group,
        sample_type,
        location,
        risks,
        persona,
        historical,
      } = params;

      const risk_score_id = risks ? risks.split(",") : [];
      const personaList = persona
        ? persona
            .split(",")
            .filter(Boolean)
            .map((p: string) => p.trim())
            .filter(Boolean)
        : [];

      const res = await queryDuck(`
      SELECT
        rs.GIS_ID,
        (SUM(CAST(rs.last_risk_category_id AS DOUBLE)) / COUNT(*)) AS avg,
        COUNT(*) AS count
      FROM risk_score_info_summaries rs
      LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
      WHERE 1=1
        AND rs.lastGroup = true
        AND rs.risk_count > 1  ${
          historical ? "" : " and first_risk_date >= '2025-05-01' "
        } 
        AND da.labName = 'Radioactivity'
        ${
          start_date && end_date
            ? `
              AND (
                CAST(rs.first_risk_date AS DATE) BETWEEN '${start_date}' AND '${end_date}'
                OR
                CAST(rs.last_risk_date  AS DATE) BETWEEN '${start_date}' AND '${end_date}'
              )
            `
            : ""
        }
        ${location ? `AND rs.GIS_ID = '${location}'` : ""}
        ${analyte ? `AND da.name = '${analyte}'` : ""}
        ${category ? `AND da.labName = '${category}'` : ""}
        ${group ? `AND da.Analyte_Group = '${group}'` : ""}
        ${
          personaList.length
            ? `AND da.persona IN (${personaList
                .map((p: string) => `'${p.replace(/'/g, "''")}'`)
                .join(",")})`
            : ""
        }
        ${
          risk_score_id.length
            ? `AND rs.last_risk_category_id IN (${risk_score_id
                .map((r: string) => Number(r))
                .join(",")})`
            : ""
        }
      GROUP BY rs.GIS_ID
      ORDER BY (SUM(CAST(rs.last_risk_category_id AS DOUBLE)) / COUNT(*)) DESC, COUNT(*) DESC
      LIMIT 10
    `);

      return {
        id: "3",
        title: `Top 10 Locations with Radioactivity Risks`,
        type: "bar",
        data: res.map((item: any) => ({
          x: item.GIS_ID,
          y1: item.avg,
        })),
        y_labels: res.map((r: any) => `count of risks: ${r.count}`),
        x_label: "Location",
        y1_label: "Count Of Risk Detected Sample",
      };
    },
  },
];
