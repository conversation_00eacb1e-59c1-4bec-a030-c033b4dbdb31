export default {
  sample_card: {
    table: "dwh.dim_sample ds",

    join: `
        INNER JOIN dwh.fact_test_results fts ON ds.sample_key = fts.sample_key
        INNER JOIN dwh.dim_analyte a ON a.analyte_key = fts.analyte_key
        INNER JOIN dwh.dim_location dl ON fts.location_key = dl.location_key
        INNER JOIN dwh.dim_test dt ON fts.test_key = dt.test_key
      `,

    params: ({ analyte, category, group, sample_type, location }) => `
        ${analyte ? `AND a.analyte_name = '${analyte}'` : ""}
        ${category ? `AND a.lab_classification = '${category}'` : ""}
        ${group ? `AND a.analyte_group = '${group}'` : ""}
        ${sample_type ? `AND dt.test_type = '${sample_type}'` : ""}
        ${location ? `AND dl.catchment_name = '${location}'` : ""}
      `,
  },

  sample_graph: {
    table: "dwh.dim_sample ds",

    join: `
        INNER JOIN dwh.fact_test_results fts ON ds.sample_key = fts.sample_key
        INNER JOIN dwh.dim_analyte a ON a.analyte_key = fts.analyte_key
        INNER JOIN dwh.dim_location dl ON fts.location_key = dl.location_key
        INNER JOIN dwh.dim_test dt ON fts.test_key = dt.test_key
      `,

    params: ({ analyte, category, group, sample_type, location }) => `
        ${analyte ? `AND a.analyte_name = '${analyte}'` : ""}
        ${category ? `AND a.lab_classification = '${category}'` : ""}
        ${group ? `AND a.analyte_group = '${group}'` : ""}
        ${sample_type ? `AND dt.test_type = '${sample_type}'` : ""}
        ${location ? `AND dl.catchment_name = '${location}'` : ""}
      `,
  },

  analyte_graph: {
    table: "dwh.dim_analyte an",

    join: `
      INNER JOIN dwh.fact_test_results fts ON an.analyte_key = fts.analyte_key
      INNER JOIN dwh.dim_sample ds ON fts.sample_key = ds.sample_key
      INNER JOIN dwh.dim_analyte a ON a.analyte_key = fts.analyte_key
      INNER JOIN dwh.dim_location dl ON fts.location_key = dl.location_key
      INNER JOIN dwh.dim_test dt ON fts.test_key = dt.test_key
`,

    params: ({ analyte, category, group, sample_type, location }) => `
      ${analyte ? `AND a.analyte_name = '${analyte}'` : ""}
      ${category ? `AND a.lab_classification = '${category}'` : ""}
      ${group ? `AND a.analyte_group = '${group}'` : ""}
      ${sample_type ? `AND dt.test_type = '${sample_type}'` : ""}
      ${location ? `AND dl.catchment_name = '${location}'` : ""}
`,
  },
  risk_graph: {
    table: "analytics.risk_scores_final rs",
    join: `
    inner join dwh.dim_sample ds  on rs.sample_key = ds.sample_key
    JOIN dwh.dim_analyte a ON rs.analyte_key = a.analyte_key
    JOIN dwh.dim_location dl ON rs.location_key = dl.location_key
`,

    params: ({ analyte, category, group, sample_type, location }) => `
${analyte ? `AND a.analyte_name = '${analyte}'` : ""}
${category ? `AND a.lab_classification = '${category}'` : ""}
${group ? `AND a.analyte_group = '${group}'` : ""}
${sample_type ? `AND dt.test_type = '${sample_type}'` : ""}
${location ? `AND dl.catchment_name = '${location}'` : ""}
`,
  },
};
