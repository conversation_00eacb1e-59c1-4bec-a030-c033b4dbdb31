import Fastify from "fastify";
import swagger from "@fastify/swagger";
import swaggerUi from "@fastify/swagger-ui";
import { Type } from "@sinclair/typebox";
import { registerRoutes } from "./routes";
import cors from "@fastify/cors"; // <-- Add this
import initDb from "./utils/db"; // <-- Add this
import { initDuckDB } from "./utils/duckdb";

const buildApp = async () => {
  const fastify = Fastify({ logger: true });
  await fastify.register(cors, {
    origin: "*", // Adjust to restrict in production
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  });
  await initDb();
  await initDuckDB();

  // Register Swagger
  await fastify.register(swagger, {
    swagger: {
      info: {
        title: "Fastify API",
        description: "API documentation",
        version: "0.0.1",
      },
      // host: "localhost:8080",
      // schemes: ["http"],
      // consumes: ["application/json"],
      // produces: ["application/json"],
    },
  });

  // Register Swagger UI
  await fastify.register(swaggerUi, {
    routePrefix: "/docs",
    uiConfig: {
      docExpansion: "full",
      deepLinking: false,
    },
  });

  //   Example route using TypeBox
  fastify.get(
    "/",
    {
      schema: {
        description: "Returns a greeting",
        tags: ["Example"],
        summary: "Hello endpoint",
        response: {
          200: Type.Object({
            msg: Type.String(),
          }),
        },
      },
    },
    async () => {
      return { msg: "working!" };
    }
  );

  // Register all modular routes
  await registerRoutes(fastify);

  await fastify.ready();
  fastify.swagger();

  const PORT = parseInt("8081");

  await fastify.listen({ port: PORT, host: "0.0.0.0" });
  console.info("Docs: http://localhost:8081/docs");
  console.info("Base: http://localhost:8081");
};

buildApp().catch(console.error);
