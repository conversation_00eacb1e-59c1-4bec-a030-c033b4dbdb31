export const analyte_insights_cards = {
  1: {
    id: 1,
    name: "Sample Presence",
    type: "two_numbers",
    left_side: {
      main: {
        value: 2757,
        label: "Present",
      },
      legend: {
        type: "change",
        pct: 8.3,
        direction: "down",
        prefix: "%",
      },
    },
    right_side: {
      main: {
        value: 885.52,
        label: "Absent",
      },
      legend: {
        type: "change",
        pct: 5.7,
        direction: "up",
        prefix: "%",
      },
    },
    list: null,
  },
  2: {
    id: 2,
    name: "Presence Rate",
    type: "two_numbers",
    left_side: {
      main: {
        value: 78,
        label: "Active",
      },
      legend: {
        type: "change",
        pct: 5.7,
        direction: "up",
        prefix: "%",
      },
    },
    right_side: {
      main: {
        value: 85,
        label: "Forecast",
      },
      legend: {
        type: "margin",
        amount: 4,
        prefix: "%",
        label: "Uncertainty",
      },
    },
    list: null,
  },
  3: {
    id: 3,
    name: "Concentration Trend",
    type: "two_numbers",
    left_side: {
      main: {
        value: 5.2,
        label: "Active",
      },
      legend: {
        type: "change",
        pct: 12.5,
        direction: "up",
        prefix: "%",
      },
    },
    right_side: {
      main: {
        value: 4.3,
        label: "Forecast",
      },
      legend: {
        type: "margin",
        amount: 4,
        prefix: "%",
        label: "Uncertainty",
      },
    },
    list: null,
  },
  4: {
    id: 4,
    name: "High-Change Regions",
    type: "two_numbers",
    left_side: {
      main: {
        value: 12,
        label: "Active",
      },
      legend: {
        type: "change",
        pct: 0,
        direction: "right",
        prefix: "%",
      },
    },
    right_side: {
      main: {
        value: 20,
        label: "Forecast",
      },
      legend: {
        type: "margin",
        amount: 4,
        prefix: "%",
        label: "Uncertainty",
      },
    },
    list: null,
  },
  5: {
    id: 5,
    name: "Flagged Samples",
    type: "two_numbers",
    left_side: {
      main: {
        value: 254,
        label: "Active",
      },
      legend: {
        type: "change",
        pct: 5.7,
        direction: "up",
        prefix: "%",
      },
    },
    right_side: {
      main: {
        value: 394,
        label: "Forecast",
      },
      legend: {
        type: "margin",
        amount: 4,
        prefix: "%",
        label: "Uncertainty",
      },
    },
    list: null,
  },
};
