import { FastifyPluginAsync } from "fastify";
import { Type, Static } from "@sinclair/typebox";
import { analyte_insights_cards } from "./cards";
import { allWidgets } from "../../config";

const analyteInsightsRoute: FastifyPluginAsync = async (fastify) => {
  const ParamsSchema = Type.Object({
    level: Type.String({
      enum: ["mid", "high", "tech"],
    }),
  });

  const QuerySchema = Type.Object({
    level: Type.Optional(
      Type.String({
        enum: ["mid", "high", "tech"],
      })
    ),
    start_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2022-12-31" })
    ),
    end_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2025-08-03" })
    ),
    cards: Type.Optional(
      Type.String({
        description: "Comma-separated list of card IDs (1,2,3)",
      })
    ),
    sample_type: Type.Optional(
      Type.String({
        description: "sample_type",
      })
    ),
    location: Type.Optional(
      Type.String({
        description: "location",
      })
    ),
    analyte: Type.Optional(
      Type.String({
        description: "analyte",
      })
    ),
    group: Type.Optional(
      Type.String({
        description: "group",
      })
    ),
    category: Type.Optional(
      Type.String({
        description: "category",
      })
    ),
    lab: Type.Optional(
      Type.String({
        description: "Lab from the Lookup",
      })
    ),
  });

  fastify.get(
    "/analyte_insights_cards/all",
    {
      schema: {
        tags: ["analyte_insights_cards_group"],
        summary: "Get all analyte_insights_cards",
        response: {
          200: Type.Array(
            Type.Object({
              id: Type.Number(),
              name: Type.String(),
            })
          ),
        },
      },
    },
    async (request) => {
      return Object.keys(analyte_insights_cards).map((i) => {
        return {
          id: i,
          name: analyte_insights_cards[i].name,
        };
      });
    }
  );

  fastify.get(
    "/analyte_insights_cards",
    {
      schema: {
        tags: ["analyte_insights_cards_group"],
        summary: "Get analyte insights cards by Ids",
        // params: ParamsSchema,
        querystring: QuerySchema,
      },
    },
    async (request) => {
      const {
        start_date,
        end_date,
        cards,
        analyte,
        category,
        group,
        location,
        sample_type,
        level,
        lab,
      } = request.query as Static<typeof QuerySchema>;

      const cardsIds = cards?.split(",");

      const results = await Promise.all(
        Object.keys(allWidgets.analyte_insights_cards).map(async (i) => {
          if (!cardsIds || !cardsIds.length || cardsIds.includes(String(i))) {
            const cardRes = await allWidgets.analyte_insights_cards[
              i as keyof (typeof allWidgets)["analyte_insights_cards"]
            ].get({
              start_date,
              end_date,
              cards,
              analyte,
              category,
              group,
              location,
              sample_type,
              level,
              lab,
            });
            return {
              ...cardRes,
            };
          }
          return null;
        })
      );

      return results.filter(Boolean); // Remove nulls (non-matching cards)
    }
  );
};

export default analyteInsightsRoute;
