import { FastifyPluginAsync } from "fastify";
import { Type, Static } from "@sinclair/typebox";
import { query } from "../../utils/db";
import { query as queryDuck } from "../../utils/duckdb";
const sqlEscape = (s: string) => s;

const genomRoute: FastifyPluginAsync = async (fastify) => {
  const QuerySchema = Type.Object({
    analyte: Type.Optional(Type.String()),
    location: Type.Optional(Type.String()),
    start_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2022-12-31" })
    ),
    end_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2025-08-03" })
    ),
  });

  fastify.get(
    "/genom/lookup",
    {
      schema: {
        tags: ["genom"],
      },
    },
    async () => {
      // Distinct analyte (organism) names 340049
      return await queryDuck(`
  select organism_level
  FROM bix_mapping group by organism_level
        `);
      const analyte = await queryDuck(`
        SELECT DISTINCT m.organism_name AS name
        FROM bix_mapping AS m
        WHERE m.organism_name IS NOT NULL
        group by m.organism_name; 
        `);

      // Distinct GIS + Location (only points that appear in WWML samples)
      const location = await queryDuck(`
        SELECT DISTINCT
          sp.GISID          AS gisid,
          sp.Location       AS location
        FROM sampling_points sp
        INNER JOIN sample sa
          ON sa.c_gisid = sp.GISID
         AND sa.template = 'WWML'
        WHERE sp.GISID IS NOT NULL
          AND sp.Location IS NOT NULL
          group by sp.GISID, sp.Location
      `);

      return { analyte, location };
    }
  );

  // ---------- GRAPH 2 (PHYLUM) ----------
  fastify.get(
    "/genom/phylum",
    {
      schema: { tags: ["genom"], querystring: QuerySchema },
    },
    async (request) => {
      const { analyte, location, start_date, end_date } =
        request.query as Static<typeof QuerySchema>;

      const where: string[] = ["p.final_result = 1", "sa.template = 'WWML'"];

      if (start_date && end_date) {
        where.push(
          `(sa.sampled_date >= '${sqlEscape(
            start_date
          )}' AND sa.sampled_date <= '${sqlEscape(end_date)}')`
        );
      }
      if (analyte) {
        const a = sqlEscape(analyte);
        where.push(
          `(m.organism_name ILIKE '%' || '${a}' || '%' OR m.organism_name = '${a}')`
        );
      }
      if (location) {
        const loc = sqlEscape(location); // treat as GISID
        where.push(`(sp.GISID = '${loc}')`);
      }

      const rows = await queryDuck(`
      SELECT DISTINCT 
          sa.text_id AS Text_ID,
          sp.Community AS Collection_Point,
          sp.Location,
          sp.Latitude,
          sp.Longitude,
          sp.GISID,
          sa.sampled_date AS Date, 
          p.sample_name AS sample_id,
          m.organism_name AS result_name,
          p.count AS result_value,
          p.percentage,
          quantile_cont(p.count, 0.5) 
              OVER (PARTITION BY m.organism_name) AS Median
      FROM bix_phylum AS p
      INNER JOIN sample sa
          ON sa.text_id = p.sample_name
         AND sa.template = 'WWML'
      INNER JOIN sampling_points sp
          ON sp.GISID = sa.c_gisid
      INNER JOIN bix_mapping m
          ON p.tax_id = m.tax_id
      WHERE ${where.join(" AND ")}
    `);

      return { rows };
    }
  );

  fastify.get(
    "/genom/species",
    {
      schema: { tags: ["genom"], querystring: QuerySchema },
    },
    async (request) => {
      const { analyte, location, start_date, end_date } =
        request.query as Static<typeof QuerySchema>;

      const where: string[] = [
        "s.final_result = 1",
        "sa.template = 'WWML'",
        "sa.c_gisid IS NOT NULL",
        "sp.Location IS NOT NULL",
      ];

      if (start_date && end_date) {
        where.push(
          `(sa.sampled_date >= '${sqlEscape(
            start_date
          )}' AND sa.sampled_date <= '${sqlEscape(end_date)}')`
        );
      }
      if (analyte) {
        const a = sqlEscape(analyte);
        where.push(
          `(m.organism_name ILIKE '%' || '${a}' || '%' OR m.organism_name = '${a}')`
        );
      }
      if (location) {
        const loc = sqlEscape(location); // treat as GISID
        where.push(`(sp.GISID = '${loc}')`);
      }

      const rows = await queryDuck(`
      SELECT DISTINCT 
          sa.text_id AS Text_ID,
          sp.Community,
          sp.Location,
          sp.Latitude,
          sp.Longitude,
          sp.GISID,
          sa.sampled_date AS Date, 
          s.sample_name AS sample_id,
          s.tax_id,
          m.organism_name AS result_name,
          m.organism_level,
          s.count AS result_value,
          sa.sample_number AS SampleNumber,
          s.percentage,
          quantile_cont(s.count, 0.5) 
              OVER (PARTITION BY m.organism_name) AS Median
      FROM bix_species AS s
      INNER JOIN sample sa 
          ON sa.text_id = s.sample_name
      INNER JOIN sampling_points sp 
          ON sp.GISID = sa.c_gisid
      INNER JOIN bix_mapping m 
          ON s.tax_id = m.tax_id
      WHERE ${where.join(" AND ")}
    `);

      return { rows };
    }
  );

  fastify.get(
    "/genom/genus",
    {
      schema: {
        tags: ["genom"],
        querystring: QuerySchema,
      },
    },
    async (request) => {
      const { analyte, location, start_date, end_date } =
        request.query as Static<typeof QuerySchema>;

      const res = await queryDuck(`
     	  SELECT DISTINCT 
    m.organism_name AS result_name,
    sa.text_id AS Text_ID,
    sp.Community AS Collection_Point,
    sp.Location,
    sp.GISID,
    sa.sampled_date AS Date, 
    g.sample_name AS sample_id,
    g.count AS result_value,
    g.percentage,
    quantile_cont(g.count, 0.5) 
        OVER (PARTITION BY m.organism_name) AS Median
FROM bix_genus AS g
INNER JOIN sample sa
    ON sa.text_id = g.sample_name
   AND sa.template = 'WWML'
INNER JOIN sampling_points sp 
    ON sp.GISID = sa.c_gisid
INNER JOIN bix_mapping m
    ON g.tax_id = m.tax_id
WHERE 
 g.final_result = 1;

 ${
   start_date && end_date
     ? `AND (sa.sampled_date >= '${start_date}' AND sa.sampled_date <= '${end_date}')`
     : ""
 }
${analyte ? `AND m.organism_name = '${analyte}' ` : ""}
${location ? ` AND (sp.GISID = '${location}') ` : ""}
  
      `);

      return {
        analyte,
        location,
      };
    }
  );
};

export default genomRoute;
