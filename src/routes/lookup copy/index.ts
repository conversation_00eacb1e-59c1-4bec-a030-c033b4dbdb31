import { FastifyPluginAsync } from "fastify";
import { Type, Static } from "@sinclair/typebox";
import { query } from "../../utils/db";
import { query as queryDuck } from "../../utils/duckdb";

const lookupRoute: FastifyPluginAsync = async (fastify) => {
  const QuerySchema = Type.Object({
    persona: Type.Optional(
      Type.String({
        enum: ["Environmental", "Security", "Health"],
      })
    ),
  });

  fastify.get(
    "/lookup",
    {
      schema: {
        tags: ["lookup"],
        querystring: QuerySchema,
      },
    },
    async (request) => {
      const { persona } = request.query as Static<typeof QuerySchema>;

      const analyte = await queryDuck(`
     	  select da.persona, a."labName" as analyte_category, a.analyte_group, a.name as analyte_name, a.sample_type, a.source_data_flag, a."Analyte_Group" , a."Lab"   from 
         summary_results_unified a
         inner join dim_analyte da on da.name = a.name
         where 1=1 
         ${persona ? `AND da.persona = '${persona}'` : ""} 
         group by da.persona, a."labName", a.analyte_group, a.name, a.sample_type, a.source_data_flag, a."Analyte_Group", a."Lab"
      `);

      const location = await queryDuck(`
        select "Location" as location_name, "CatchmentArea" as catchment_name , "GIS_ID" from summary_results_unified group by location, catchment_name, GIS_ID where GIS_ID is not null
        `);

      return {
        analyte,
        location,
      };
    }
  );
};

export default lookupRoute;
