import { FastifyPluginAsync } from "fastify";
import { Type, Static } from "@sinclair/typebox";
import { mapsConfig } from "../../config/maps";

const mapsRoute: FastifyPluginAsync = async (fastify) => {
  const QuerySchema = Type.Object({
    level: Type.Optional(
      Type.String({
        enum: ["mid", "high", "tech"],
      })
    ),
    date_frame: Type.String({
      enum: ["week", "month", "quarter", "year"],
    }),

    collection: Type.String({
      enum: [
        "sample_overview_cards",
        "analyte_insights_cards",
        "risk_trends_cards",
      ],
    }),
    start_date: Type.String({ format: "date", description: "e.g 2022-12-31" }),
    end_date: Type.String({ format: "date", description: "e.g 2025-08-03" }),
    card_id: Type.Optional(
      Type.String({
        description: "card ID",
      })
    ),
    sample_type: Type.Optional(
      Type.String({
        description: "sample_type",
      })
    ),
    location: Type.Optional(
      Type.String({
        description: "location",
      })
    ),
    analyte: Type.Optional(
      Type.String({
        description: "analyte",
      })
    ),
    group: Type.Optional(
      Type.String({
        description: "group",
      })
    ),
    category: Type.Optional(
      Type.String({
        description: "category",
      })
    ),
    risks: Type.Optional(
      Type.String({
        description: "1,2,3,4,5",
      })
    ),
    persona: Type.Optional(
      Type.String({
        description: "Health,Security,Environmental",
      })
    ),
    historical: Type.Optional(
      Type.Boolean({
        description: "Show historical risks",
      })
    ),
  });

  fastify.get(
    "/map",
    {
      schema: {
        tags: ["map"],
        summary: "Get map data for a card",
        querystring: QuerySchema,
      },
    },
    async (request) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        analyte,
        category,
        group,
        sample_type,
        location,
        level,
        risks,
        persona,
        historical,
      } = request.query as Static<typeof QuerySchema>;

      const mapsData = await mapsConfig
        .find((m) => {
          return (
            ((m.card_ids.includes(card_id) || m.card_ids.includes("*")) &&
              m.collections.includes(collection)) ||
            m.collections.includes("*")
          );
        })
        ?.get({
          start_date,
          end_date,
          card_id,
          collection,
          date_frame,
          analyte,
          category,
          group,
          location,
          sample_type,
          persona,
          risks,
          historical,
        });

      return mapsData || null;
    }
  );
};

export default mapsRoute;
