import { FastifyPluginAsync } from "fastify";
import { Type, Static } from "@sinclair/typebox";
import { plotsConfig } from "../../config/plots";
// import { sample_overview_cards } from "../sample_overview_cards/cards";
// import { analyte_insights_cards } from "../analyte_insights_cards/cards";
// import { risk_trends_cards } from "../risk_trends_cards/cards";

const plotsRoute: FastifyPluginAsync = async (fastify) => {
  const QuerySchema = Type.Object({
    level: Type.Optional(
      Type.String({
        enum: ["mid", "high", "tech"],
      })
    ),
    date_frame: Type.String({
      enum: ["week", "month", "quarter", "year"],
    }),

    collection: Type.String({
      enum: [
        "sample_overview_cards",
        "analyte_insights_cards",
        "risk_trends_cards",
      ],
    }),
    start_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2007-07-07" })
    ),
    end_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2007-07-07" })
    ),
    card_id: Type.Optional(
      Type.String({
        description: "card ID",
      })
    ),
    risks: Type.Optional(
      Type.String({
        description: "1,2,3,4,5",
      })
    ),
    sample_type: Type.Optional(
      Type.String({
        description: "sample_type",
      })
    ),
    location: Type.Optional(
      Type.String({
        description: "location",
      })
    ),
    analyte: Type.Optional(
      Type.String({
        description: "analyte",
      })
    ),
    group: Type.Optional(
      Type.String({
        description: "group",
      })
    ),
    category: Type.Optional(
      Type.String({
        description: "category",
      })
    ),
    persona: Type.Optional(
      Type.String({
        description: "Health,Security,Environmental",
      })
    ),
    historical: Type.Optional(
      Type.Boolean({
        description: "Show historical risks",
      })
    ),
  });

  fastify.get(
    "/plot",
    {
      schema: {
        tags: ["plot"],
        summary: "Get plots for a card",
        // params: ParamsSchema,
        querystring: QuerySchema,
      },
    },
    async (request) => {
      // const { level } =  request.params as Static<typeof ParamsSchema>;
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        analyte,
        category,
        group,
        sample_type,
        location,
        level,
        risks,
        persona,
        historical,
      } = request.query as Static<typeof QuerySchema>;
      const plotsData = await Promise.all(
        plotsConfig
          .filter((p) => {
            return (
              p.collections.includes(collection) &&
              (p.card_ids.includes(card_id) || p.card_ids.includes("*"))
            );
          })
          .filter(Boolean)
          .map(
            async (plot) =>
              await plot.get({
                start_date,
                end_date,
                card_id,
                collection,
                date_frame,
                analyte,
                category,
                group,
                location,
                sample_type,
                risks,
                persona,
                historical,
              })
          )
      );

      return plotsData;
    }
  );
};

export default plotsRoute;
