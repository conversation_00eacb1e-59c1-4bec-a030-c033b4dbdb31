export const risk_trends_cards = {
  1: {
    id: 1,
    name: "Total Risk",
    type: "two_numbers",
    left_side: {
      main: {
        value: 167,
        label: "Active",
      },
      legend: {
        type: "change",
        pct: 5.7,
        direction: "down",
        prefix: "%",
      },
    },

    right_side: {
      main: {
        value: 200,
        label: "Forecast",
      },
      legend: {
        type: "margin",
        amount: 14,
        prefix: "%",
        label: "Uncertainty",
      },
    },
    list: [
      {
        name: "Health",
        type: "two_numbers",
        left_side: {
          main: {
            value: 35,
            label: "Active",
          },
          legend: {
            type: "change",
            pct: 2.3,
            direction: "up",
            prefix: "%",
          },
        },
        right_side: {
          main: {
            value: 45,
            label: "Forecast",
          },
          legend: {
            type: "margin",
            amount: 15,
            prefix: "%",
            label: "Uncertainty",
          },
        },
      },
      {
        name: "Environmental",
        type: "two_numbers",
        left_side: {
          main: {
            value: 28,
            label: "Active",
          },
          legend: {
            type: "change",
            pct: 1.9,
            direction: "up",
            prefix: "%",
          },
        },
        right_side: {
          main: {
            value: 38,
            label: "Forecast",
          },
          legend: {
            type: "margin",
            amount: 15,
            prefix: "%",
            label: "Uncertainty",
          },
        },
      },
      {
        name: "Security",
        type: "two_numbers",
        left_side: {
          main: {
            value: 22,
            label: "Active",
          },
          legend: {
            type: "change",
            pct: 1.5,
            direction: "up",
            prefix: "%",
          },
        },
        right_side: {
          main: {
            value: 30,
            label: "Forecast",
          },
          legend: {
            type: "margin",
            amount: 12,
            prefix: "%",
            label: "Uncertainty",
          },
        },
      },
    ],
  },
  2: {
    id: 2,
    name: "Emerging Threats",
    type: "two_numbers",
    left_side: {
      main: {
        value: 892,
        label: "Active",
      },
      legend: {
        type: "change",
        pct: 8.3,
        direction: "down",
        prefix: "%",
      },
    },

    right_side: {
      main: {
        value: 1000,
        label: "Forecast",
      },
      legend: {
        type: "margin",
        amount: 67,
        prefix: "%",
        label: "Uncertainty",
      },
    },
    list: [
      {
        name: "Health",
        type: "two_numbers",
        left_side: {
          main: {
            value: 35,
            label: "Active",
          },
          legend: {
            type: "change",
            pct: 2.3,
            direction: "up",
            prefix: "%",
          },
        },
        right_side: {
          main: {
            value: 45,
            label: "Forecast",
          },
          legend: {
            type: "margin",
            amount: 15,
            prefix: "%",
            label: "Uncertainty",
          },
        },
      },
      {
        name: "Environmental",
        type: "two_numbers",
        left_side: {
          main: {
            value: 28,
            label: "Active",
          },
          legend: {
            type: "change",
            pct: 1.9,
            direction: "up",
            prefix: "%",
          },
        },
        right_side: {
          main: {
            value: 38,
            label: "Forecast",
          },
          legend: {
            type: "margin",
            amount: 15,
            prefix: "%",
            label: "Uncertainty",
          },
        },
      },
      {
        name: "Security",
        type: "two_numbers",
        left_side: {
          main: {
            value: 22,
            label: "Active",
          },
          legend: {
            type: "change",
            pct: 1.5,
            direction: "up",
            prefix: "%",
          },
        },
        right_side: {
          main: {
            value: 30,
            label: "Forecast",
          },
          legend: {
            type: "margin",
            amount: 12,
            prefix: "%",
            label: "Uncertainty",
          },
        },
      },
    ],
  },
  3: {
    id: 3,
    name: "Radioactivity Risks",
    type: "two_numbers",
    left_side: {
      main: {
        value: 456,
        label: "Active",
      },
      legend: {
        type: "change",
        pct: 0,
        direction: "center",
        prefix: "%",
      },
    },

    right_side: {
      main: {
        value: 600,
        label: "Forecast",
      },
      legend: {
        type: "margin",
        amount: 28,
        prefix: "%",
        label: "Uncertainty",
      },
    },
    list: null,
  },
  4: {
    id: 4,
    name: "Critical Regions",
    type: "two_numbers",
    left_side: {
      main: {
        value: 10,
        label: "Active",
      },
      legend: {
        type: "change",
        pct: 12.5,
        direction: "down",
        prefix: "%",
      },
    },

    right_side: {
      main: {
        value: 12,
        label: "Forecast",
      },
      legend: {
        type: "margin",
        amount: 4,
        prefix: "%",
        label: "Uncertainty",
      },
    },
    list: null,
  },
  
};
