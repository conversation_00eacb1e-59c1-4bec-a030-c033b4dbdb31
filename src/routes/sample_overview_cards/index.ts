import { FastifyPluginAsync } from "fastify";
import { Type, Static } from "@sinclair/typebox";
import { allWidgets } from "../../config";

const summaryRoute: FastifyPluginAsync = async (fastify) => {
  const QuerySchema = Type.Object({
    level: Type.Optional(
      Type.String({
        enum: ["mid", "high", "tech"],
      })
    ),
    start_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2007-07-07" })
    ),
    end_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2007-07-07" })
    ),
    cards: Type.Optional(
      Type.String({
        description: "Comma-separated list of card IDs (1,2,3)",
      })
    ),
    sample_type: Type.Optional(
      Type.String({
        description: "sample_type",
      })
    ),
    location: Type.Optional(
      Type.String({
        description: "location",
      })
    ),
    analyte: Type.Optional(
      Type.String({
        description: "analyte",
      })
    ),
    group: Type.Optional(
      Type.String({
        description: "group",
      })
    ),
    category: Type.Optional(
      Type.String({
        description: "category",
      })
    ),
    Lab: Type.Optional(
      Type.String({
        description: "Lab from the Lookup",
      })
    ),
  });

  fastify.get(
    "/sample_overview_cards/all",
    {
      schema: {
        tags: ["sample_overview_cards_group"],
        summary: "Get all sample_overview_cards",
        response: {
          200: Type.Array(
            Type.Object({
              id: Type.Number(),
              name: Type.String(),
            })
          ),
        },
      },
    },
    async (request) => {
      return Object.keys(allWidgets.sample_overview_cards).map((i) => {
        return { ...allWidgets.sample_overview_cards[i].info };
      });
    }
  );

  fastify.get(
    "/sample_overview_cards",
    {
      schema: {
        tags: ["sample_overview_cards_group"],
        summary: "Get summary cards by Ids",
        // params: ParamsSchema,
        querystring: QuerySchema,
      },
    },
    async (request) => {
      // const { level } = request.params as Static<typeof ParamsSchema>;
      const {
        start_date,
        end_date,
        cards,
        analyte,
        category,
        group,
        location,
        sample_type,
        level,
        Lab,
      } = request.query as Static<typeof QuerySchema>;

      const cardsIds = cards?.split(",");

      const results = await Promise.all(
        Object.keys(allWidgets.sample_overview_cards).map(async (i) => {
          if (!cardsIds || !cardsIds.length || cardsIds.includes(String(i))) {
            const cardRes = await allWidgets.sample_overview_cards[
              i as keyof (typeof allWidgets)["sample_overview_cards"]
            ].get({
              start_date,
              end_date,
              cards,
              analyte,
              category,
              group,
              location,
              sample_type,
              level,
              Lab,
            });
            return {
              ...cardRes,
            };
          }
          return null;
        })
      );

      return results.filter(Boolean); // Remove nulls (non-matching cards)
    }
  );
};

export default summaryRoute;
