import { FastifyPluginAsync } from "fastify";
import { Type, Static } from "@sinclair/typebox";
import { query } from "../../utils/db";
import { showTrue } from "../../utils/utils";

const testRoute: FastifyPluginAsync = async (fastify) => {
  const ParamsSchema = Type.Object({
    level: Type.String({
      enum: ["mid", "high", "tech"],
    }),
  });

  const QuerySchema = Type.Object({
    start_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2007-07-07" })
    ),
    end_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2007-07-07" })
    ),
    cards: Type.Optional(
      Type.String({
        description: "Comma-separated list of card IDs (1,2,3)",
      })
    ),
    sample_type: Type.Optional(
      Type.String({
        description: "sample_type",
      })
    ),
    location: Type.Optional(
      Type.String({
        description: "location",
      })
    ),
    analyte: Type.Optional(
      Type.String({
        description: "analyte",
      })
    ),
    group: Type.Optional(
      Type.String({
        description: "group",
      })
    ),
    category: Type.Optional(
      Type.String({
        description: "category",
      })
    ),
  });
  fastify.get(
    "/test",
    {
      schema: {
        tags: ["1"],
        summary: "Get plot by Ids",
        // params: ParamsSchema,
        querystring: QuerySchema,
      },
    },
    async (request) => {
      const { level } = request.params as Static<typeof ParamsSchema>;
      const {
        start_date,
        end_date,
        analyte,
        cards,
        category,
        group,
        location,
        sample_type,
      } = request.query as Static<typeof QuerySchema>;

      const res = await query(`
      SELECT
      COUNT(DISTINCT ds.sample_id) as total_samples_collected
      FROM dwh.dim_sample ds
      WHERE 1=1
        ${
          start_date && end_date
            ? `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`
            : ""
        }
          `);

      console.dir(res, { depth: null });
      return res;

      // return [line, bar];
    }
  );
};

export default testRoute;
