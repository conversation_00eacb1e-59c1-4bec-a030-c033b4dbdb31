WITH base AS (
    SELECT
        s."GIS_ID",
        s."name",
        s."SAMPLE_NUMBER",
        s."sampled_date",
        s."DATE_COMPLETED",
        s."rawResult",
        l.loq_value
    FROM
        summary_results_unified s
        JOIN analyte_loq_clean l ON s."name" = l."Analyte"
    WHERE
        s."ResultStatus" = 'A'
        AND s."GIS_ID" = 'e3568f19-fc10-4bb5-a764-e7d1f97708be'
        AND s."name" = 'Tramadol'
        AND TRY_CAST(s."rawResult" AS DOUBLE) IS NOT NULL
        AND l.loq_value IS NOT NULL
),
ranked AS (
    SELECT
        b.*,
        ROW_NUMBER() OVER (
            PARTITION BY b."SAMPLE_NUMBER"
            ORDER BY
                (b."DATE_COMPLETED" IS NULL),
                b."DATE_COMPLETED" DESC,
                b."sampled_date" DESC
        ) AS rn
    FROM
        base b
),
series AS (
    -- latest per sample, numeric
    SELECT
        "GIS_ID",
        "name",
        "SAMPLE_NUMBER" AS sample_number,
        "sampled_date",
        TRY_CAST("rawResult" AS DOUBLE) AS "rawResult"
    FROM
        ranked
    WHERE
        rn = 1
),
stats AS (
    -- global stats for this series
    SELECT
        AVG("rawResult") AS mu,
        STDDEV_POP("rawResult") AS sigma,
        MEDIAN("rawResult") AS med,
        QUANTILE("rawResult", 0.25) AS q1,
        QUANTILE("rawResult", 0.75) AS q3
    FROM
        series
),
absdev AS (
    -- absolute deviations for MAD
    SELECT
        s.*,
        ABS(s."rawResult" - st.med) AS abs_dev
    FROM
        series s
        CROSS JOIN stats st
),
madval AS (
    -- MAD value
    SELECT
        MEDIAN(abs_dev) AS mad
    FROM
        absdev
),
scored AS (
    -- compute scores
    SELECT
        a."GIS_ID",
        a."name",
        a.sample_number,
        a."sampled_date",
        a."rawResult",
        st.mu,
        st.sigma,
        st.med,
        st.q1,
        st.q3,
        (st.q3 - st.q1) AS iqr,
        CASE
            WHEN st.sigma IS NULL
            OR st.sigma = 0 THEN NULL
            ELSE (a."rawResult" - st.mu) / st.sigma
        END AS zscore,
        CASE
            WHEN mv.mad IS NULL
            OR mv.mad = 0 THEN NULL
            ELSE ABS(a."rawResult" - st.med) / mv.mad
        END AS mad_score
    FROM
        absdev a
        CROSS JOIN stats st
        CROSS JOIN madval mv
)
SELECT
    "GIS_ID",
    "name",
    sample_number,
    "sampled_date",
    "rawResult",
    (ABS(zscore) > 3) AS z_anomaly
FROM
    scored
WHERE
    (ABS(zscore) > 3) -- z-score rule
    OR (
        "rawResult" < (q1 - 1.5 * iqr)
        OR "rawResult" > (q3 + 1.5 * iqr)
    ) -- IQR rule
    OR (mad_score > 3.5) -- MAD rule
ORDER BY
    "sampled_date" DESC;