CREATE
OR REPLACE TABLE summary_results_unified AS
SELECT
    false as not_anomaly,
    'routine' as "sample_type",
    'CH_Routine' AS source_data_flag,
    "ANALYSIS",
    "Analyte_Group",
    NULL AS "Analyte_group",
    "CLOSED",
    "CUSTOMER",
    "C_DELIVERED_BY",
    NULL AS "C_FINAL_ID",
    "CatchmentArea",
    "Collection_Point",
    "DATE_COMPLETED",
    "DATE_REVIEWED",
    "DATE_STARTED",
    NULL AS "Date",
    "FORMATTED_ENTRY",
    "FinalResult_Value",
    "Final_Result",
    "GIS_ID",
    NULL AS "IT_DESCRIPTION",
    "Lab",
    "Latitude",
    "Location",
    "Longitude",
    "ORIGINAL_SAMPLE",
    "PROJECT",
    "Population",
    "RESULT_NUMBER",
    "ResultStatus",
    "ResultStatusDesc",
    "Result_Analysis",
    "Result_Analysis1",
    "SAMPLED_DATE",
    "SAMPLE_NUMBER",
    "SampleStatus",
    "TEST_NUMBER",
    "TEXT_ID",
    "TestStatus",
    "TestStatusDesc",
    null as "UNITS",
    NULL AS "actual_formatted_entry",
    "casNum",
    "date_received",
    NULL AS "formatted_entry",
    "labName",
    "name",
    "projStatus",
    "rawResult",
    "reported_name",
    "resChangedOn",
    "resReviewedDate",
    "sampleTemplate"
FROM
    VW_CM_SUMMARY_RSLT
UNION
ALL
SELECT
    false as not_anomaly,
    'routine' as "sample_type",
    'MB_Routine' AS source_data_flag,
    "ANALYSIS",
    NULL AS "Analyte_Group",
    "Analyte_group",
    "CLOSED",
    "CUSTOMER",
    "C_DELIVERED_BY",
    "C_FINAL_ID",
    "CatchmentArea",
    "Collection_Point",
    "DATE_COMPLETED",
    "DATE_REVIEWED",
    "DATE_STARTED",
    "Date",
    NULL AS "FORMATTED_ENTRY",
    "FinalResult_Value",
    NULL AS "Final_Result",
    NULL AS "GIS_ID",
    "IT_DESCRIPTION",
    "Lab",
    "Latitude",
    "Location",
    "Longitude",
    "ORIGINAL_SAMPLE",
    "PROJECT",
    NULL AS "Population",
    "RESULT_NUMBER",
    "ResultStatus",
    "ResultStatusDesc",
    NULL AS "Result_Analysis",
    "Result_Analysis1",
    "SAMPLED_DATE",
    "SAMPLE_NUMBER",
    "SampleStatus",
    "TEST_NUMBER",
    "TEXT_ID",
    "TestStatus",
    "TestStatusDesc",
    null as "UNITS",
    "actual_formatted_entry",
    "casNum",
    "date_received",
    "formatted_entry",
    "labName",
    "name",
    "projStatus",
    "rawResult",
    "reported_name",
    "resChangedOn",
    "resReviewedDate",
    "sampleTemplate"
FROM
    VW_MB_SUMMARY_RSLT2
UNION
ALL
select
    false as not_anomaly,
    'ad-hoc' as "sample_type",
    'CM_Special' as source_data_flag,
    ANALYSIS,
    Analyte_Group,
    null as Analyte_group_1,
    CLOSED,
    CUSTOMER,
    C_DELIVERED_BY,
    null as C_FINAL_ID,
    null as CatchmentArea,
    -- update to add
    null as Collection_Point,
    DATE_COMPLETED,
    DATE_REVIEWED,
    DATE_STARTED,
    null as Date,
    FORMATTED_ENTRY,
    FinalResult_Value,
    null as Final_Result,
    null as GIS_ID,
    null as IT_DESCRIPTION,
    Lab,
    null as Latitude,
    location as "Location",
    null as Longitude,
    ORIGINAL_SAMPLE,
    PROJECT,
    Population,
    RESULT_NUMBER,
    ResultStatus,
    ResultStatusDesc,
    null as Result_Analysis,
    null as Result_Analysis1,
    SAMPLED_DATE,
    SAMPLE_NUMBER,
    SampleStatus,
    TEST_NUMBER,
    TEXT_ID,
    TestStatus,
    TestStatusDesc,
    null as UNITS,
    null as actual_formatted_entry,
    casNum,
    date_received,
    null as formatted_entry_1,
    labName,
    "Analyte" as "name",
    projStatus,
    rawResult,
    reported_name,
    resChangedOn,
    resReviewedDate,
    sampleTemplate
from
    vw_special_cm_res
UNION
ALL
select
    false as not_anomaly,
    'ad-hoc' as "sample_type",
    'MB_Special' as "source_data_flag",
    "limsTestName" as "ANALYSIS",
    "limsTestName" as "Analyte_Group",
    null as "Analyte_group_1",
    CLOSED,
    CUSTOMER,
    C_DELIVERED_BY,
    C_FINAL_ID,
    null as CatchmentArea,
    null as Collection_Point,
    DATE_COMPLETED,
    DATE_REVIEWED,
    DATE_STARTED,
    Date,
    "actual_formatted_entry" as "FORMATTED_ENTRY",
    "actual_formatted_entry" as "FinalResult_Value",
    null as Final_Result,
    null as GIS_ID,
    null as IT_DESCRIPTION,
    Lab,
    null as Latitude,
    location as "Location",
    null as Longitude,
    ORIGINAL_SAMPLE,
    PROJECT,
    null as Population,
    RESULT_NUMBER,
    ResultStatus,
    ResultStatusDesc,
    null as Result_Analysis,
    Result_Analysis1,
    SAMPLED_DATE,
    SAMPLE_NUMBER,
    SampleStatus,
    TEST_NUMBER,
    TEXT_ID,
    TestStatus,
    TestStatusDesc,
    null as UNITS,
    actual_formatted_entry,
    casNum,
    date_received,
    "actual_formatted_entry" as formatted_entry_1,
    labName,
    "Analyte" as name,
    projStatus,
    rawResult,
    reported_name,
    resChangedOn,
    resReviewedDate,
    sampleTemplate
from
    vw_special_mb_res