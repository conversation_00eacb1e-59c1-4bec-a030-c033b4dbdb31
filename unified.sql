CREATE
OR REPLACE TABLE summary_results_unified AS
SELECT
    'routine' AS "sample_type",
    'CH_Routine' AS source_data_flag,
    "ANALYSIS",
    "Analyte_Group",
    NULL AS "Analyte_group",
    "CLOSED",
    "CUSTOMER",
    "C_DELIVERED_BY",
    NULL AS "C_FINAL_ID",
    "CatchmentArea",
    "Collection_Point",
    CAST("DATE_COMPLETED" AS DATE) AS "DATE_COMPLETED",
    CAST("DATE_REVIEWED" AS DATE) AS "DATE_REVIEWED",
    CAST("DATE_STARTED" AS DATE) AS "DATE_STARTED",
    NULL AS "Date",
    "FORMATTED_ENTRY",
    "FinalResult_Value",
    "Final_Result",
    "GIS_ID",
    NULL AS "IT_DESCRIPTION",
    "Lab",
    "Latitude",
    "Location",
    "Longitude",
    "ORIGINAL_SAMPLE",
    "PROJECT",
    "Population",
    "RESULT_NUMBER",
    "ResultStatus",
    "ResultStatusDesc",
    "Result_Analysis",
    "Result_Analysis1",
    CAST("SAMPLED_DATE" AS DATE) AS "SAMPLED_DATE",
    "SAMPLE_NUMBER",
    "SampleStatus",
    "TEST_NUMBER",
    "TEXT_ID",
    "TestStatus",
    "TestStatusDesc",
    NULL AS "UNITS",
    NULL AS "actual_formatted_entry",
    "casNum",
    CAST("date_received" AS DATE) AS "date_received",
    NULL AS "formatted_entry",
    "labName",
    "name",
    "projStatus",
    "rawResult",
    "reported_name",
    CAST("resChangedOn" AS DATE) AS "resChangedOn",
    CAST("resReviewedDate" AS DATE) AS "resReviewedDate",
    "sampleTemplate"
FROM
    chemistry_routine_data
UNION
ALL
SELECT
    'routine' AS "sample_type",
    'MB_Routine' AS source_data_flag,
    "ANALYSIS",
    NULL AS "Analyte_Group",
    "Analyte_group",
    "CLOSED",
    "CUSTOMER",
    "C_DELIVERED_BY",
    "C_FINAL_ID",
    "CatchmentArea",
    "Collection_Point",
    CAST("DATE_COMPLETED" AS DATE) AS "DATE_COMPLETED",
    CAST("DATE_REVIEWED" AS DATE) AS "DATE_REVIEWED",
    CAST("DATE_STARTED" AS DATE) AS "DATE_STARTED",
    CAST("Date" AS DATE) AS "Date",
    NULL AS "FORMATTED_ENTRY",
    "FinalResult_Value",
    NULL AS "Final_Result",
    NULL AS "GIS_ID",
    "IT_DESCRIPTION",
    "Lab",
    "Latitude",
    "Location",
    "Longitude",
    "ORIGINAL_SAMPLE",
    "PROJECT",
    NULL AS "Population",
    "RESULT_NUMBER",
    "ResultStatus",
    "ResultStatusDesc",
    NULL AS "Result_Analysis",
    "Result_Analysis1",
    CAST("SAMPLED_DATE" AS DATE) AS "SAMPLED_DATE",
    "SAMPLE_NUMBER",
    "SampleStatus",
    "TEST_NUMBER",
    "TEXT_ID",
    "TestStatus",
    "TestStatusDesc",
    NULL AS "UNITS",
    "actual_formatted_entry",
    "casNum",
    CAST("date_received" AS DATE) AS "date_received",
    "formatted_entry",
    "labName",
    "name",
    "projStatus",
    "rawResult",
    "reported_name",
    CAST("resChangedOn" AS DATE) AS "resChangedOn",
    CAST("resReviewedDate" AS DATE) AS "resReviewedDate",
    "sampleTemplate"
FROM
    microbiology_routine_data
UNION
ALL
SELECT
    'ad-hoc' AS "sample_type",
    'CM_Special' AS source_data_flag,
    ANALYSIS,
    Analyte_Group,
    NULL AS Analyte_group_1,
    "CLOSED",
    CUSTOMER,
    C_DELIVERED_BY,
    NULL AS C_FINAL_ID,
    NULL AS CatchmentArea,
    NULL AS Collection_Point,
    CAST(DATE_COMPLETED AS DATE) AS "DATE_COMPLETED",
    CAST(DATE_REVIEWED AS DATE) AS "DATE_REVIEWED",
    CAST(DATE_STARTED AS DATE) AS "DATE_STARTED",
    NULL AS "Date",
    FORMATTED_ENTRY,
    FinalResult_Value,
    NULL AS Final_Result,
    NULL AS GIS_ID,
    NULL AS IT_DESCRIPTION,
    Lab,
    NULL AS Latitude,
    location AS "Location",
    NULL AS Longitude,
    ORIGINAL_SAMPLE,
    PROJECT,
    Population,
    RESULT_NUMBER,
    ResultStatus,
    ResultStatusDesc,
    NULL AS Result_Analysis,
    NULL AS Result_Analysis1,
    CAST(SAMPLED_DATE AS DATE) AS "SAMPLED_DATE",
    SAMPLE_NUMBER,
    SampleStatus,
    TEST_NUMBER,
    TEXT_ID,
    TestStatus,
    TestStatusDesc,
    NULL AS UNITS,
    NULL AS actual_formatted_entry,
    casNum,
    CAST(date_received AS DATE) AS "date_received",
    NULL AS formatted_entry_1,
    labName,
    "Analyte" AS "name",
    projStatus,
    rawResult,
    reported_name,
    CAST(resChangedOn AS DATE) AS "resChangedOn",
    CAST(resReviewedDate AS DATE) AS "resReviewedDate",
    sampleTemplate
FROM
    chemistry_special_data
UNION
ALL
SELECT
    'ad-hoc' AS "sample_type",
    'MB_Special' AS "source_data_flag",
    "limsTestName" AS "ANALYSIS",
    "limsTestName" AS "Analyte_Group",
    NULL AS "Analyte_group_1",
    "CLOSED",
    CUSTOMER,
    C_DELIVERED_BY,
    C_FINAL_ID,
    NULL AS CatchmentArea,
    NULL AS Collection_Point,
    CAST(DATE_COMPLETED AS DATE) AS "DATE_COMPLETED",
    CAST(DATE_REVIEWED AS DATE) AS "DATE_REVIEWED",
    CAST(DATE_STARTED AS DATE) AS "DATE_STARTED",
    CAST(Date AS DATE) AS "Date",
    "actual_formatted_entry" AS "FORMATTED_ENTRY",
    "actual_formatted_entry" AS "FinalResult_Value",
    NULL AS Final_Result,
    NULL AS GIS_ID,
    NULL AS IT_DESCRIPTION,
    Lab,
    NULL AS Latitude,
    location AS "Location",
    NULL AS Longitude,
    ORIGINAL_SAMPLE,
    PROJECT,
    NULL AS Population,
    RESULT_NUMBER,
    ResultStatus,
    ResultStatusDesc,
    NULL AS Result_Analysis,
    Result_Analysis1,
    CAST(SAMPLED_DATE AS DATE) AS "SAMPLED_DATE",
    SAMPLE_NUMBER,
    SampleStatus,
    TEST_NUMBER,
    TEXT_ID,
    TestStatus,
    TestStatusDesc,
    NULL AS UNITS,
    actual_formatted_entry,
    casNum,
    CAST(date_received AS DATE) AS "date_received",
    "actual_formatted_entry" AS formatted_entry_1,
    labName,
    "Analyte" AS name,
    projStatus,
    rawResult,
    reported_name,
    CAST(resChangedOn AS DATE) AS "resChangedOn",
    CAST(resReviewedDate AS DATE) AS "resReviewedDate",
    sampleTemplate
FROM
    microbiology_special_data;